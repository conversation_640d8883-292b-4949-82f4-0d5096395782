<meta charset="<%= @options.charset %>">
<meta name="viewport" content="width=device-width, initial-scale=1" />

<title><%= h @title %></title>

<%- if defined?(klass) -%>
  <meta name="keywords" content="ruby,<%= h "#{klass.type},#{klass.full_name}" %>">

  <%- if klass.comment.empty? -%>
    <meta name="description" content="Documentation for the <%= h "#{klass.full_name} #{klass.type}" %>">
  <%- else -%>
    <meta name="description" content="<%= h "#{klass.type} #{klass.full_name}: #{excerpt(klass.comment)}" %>">
  <%- end -%>
<%- elsif defined?(file) -%>
  <meta name="keywords" content="ruby,documentation,<%= h file.page_name %>">
  <meta name="description" content="<%= h "#{file.page_name}: #{excerpt(file.comment)}" %>">
<%- elsif @title -%>
  <meta name="keywords" content="ruby,documentation,<%= h @title %>">

  <%- if @options.main_page and
      main_page = @files.find { |f| f.full_name == @options.main_page } then %>
    <meta name="description" content="<%= h "#{@title}: #{excerpt(main_page.comment)}" %>">
  <%- else -%>
    <meta name="description" content="Documentation for <%= h @title %>">
  <%- end -%>
<%- end -%>

<%- if canonical_url = @options.canonical_root -%>
<% canonical_url = current.canonical_url if defined?(current) %>
<link rel="canonical" href="<%= canonical_url %>">
<%- end -%>

<script type="text/javascript">
  var rdoc_rel_prefix = "<%= h asset_rel_prefix %>/";
  var index_rel_prefix = "<%= h rel_prefix %>/";
</script>

<script src="<%= h asset_rel_prefix %>/js/navigation.js" defer></script>
<script src="<%= h asset_rel_prefix %>/js/search.js" defer></script>
<script src="<%= h asset_rel_prefix %>/js/search_index.js" defer></script>
<script src="<%= h asset_rel_prefix %>/js/searcher.js" defer></script>
<script src="<%= h asset_rel_prefix %>/js/darkfish.js" defer></script>

<link href="<%= h asset_rel_prefix %>/css/fonts.css" rel="stylesheet">
<link href="<%= h asset_rel_prefix %>/css/rdoc.css" rel="stylesheet">
<%- @options.template_stylesheets.each do |stylesheet| -%>
<link href="<%= h asset_rel_prefix %>/<%= File.basename stylesheet %>" rel="stylesheet">
<%- end -%>

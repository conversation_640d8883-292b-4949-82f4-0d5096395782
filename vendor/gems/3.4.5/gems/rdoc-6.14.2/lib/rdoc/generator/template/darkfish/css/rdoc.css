/*
 * "Darkfish" RDoc CSS
 * $Id: rdoc.css 54 2009-01-27 01:09:48Z deveiant $
 *
 * Author: <PERSON> <<EMAIL>>
 *
 */

/* vim: ft=css et sw=2 ts=2 sts=2 */

/* 1. Variables and Root Styles */
:root {
  --sidebar-width: 300px;
  --highlight-color: #cc342d; /* Reddish color for accents and headings */
  --secondary-highlight-color: #c83045; /* Darker reddish color for secondary highlights */
  --text-color: #505050; /* Dark bluish-grey for text */
  --background-color: #fefefe; /* Near white background */
  --code-block-background-color: #f6f6f3; /* Slightly darker grey for code blocks */
  --link-color: #42405F; /* Dark bluish-grey for links */
  --link-hover-color: var(--highlight-color); /* Reddish color on hover */
  --border-color: #e0e0e0;; /* General border color */
  --source-code-toggle-color: var(--secondary-highlight-color);
  --scrollbar-thumb-hover-background: #505050; /* Hover color for scrollbar thumb */
  --table-header-background-color: #eceaed;
  --table-td-background-color: #f5f4f6;

  /* Font family variables */
  --font-primary: 'Segoe UI', 'Verdana', 'Arial', sans-serif;
  --font-heading: 'Helvetica', 'Arial', sans-serif;
  --font-code: monospace;
}

/* 2. Global Styles */
body {
  background: var(--background-color);
  font-family: var(--font-primary);
  font-weight: 400;
  color: var(--text-color);
  line-height: 1.6;

  /* Layout */
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  margin: 0;
}

/* 3. Typography */
h1 span,
h2 span,
h3 span,
h4 span,
h5 span,
h6 span {
  position: relative;

  display: none;
  padding-left: 1em;
  line-height: 0;
  vertical-align: baseline;
  font-size: 10px;
}

h1 span { top: -1.3em; }
h2 span { top: -1.2em; }
h3 span { top: -1.0em; }
h4 span { top: -0.8em; }
h5 span { top: -0.5em; }
h6 span { top: -0.5em; }

h1:hover span,
h2:hover span,
h3:hover span,
h4:hover span,
h5:hover span,
h6:hover span {
  display: inline;
}

h1:target,
h2:target,
h3:target,
h4:target,
h5:target,
h6:target {
  margin-left: -10px;
  border-left: 10px solid var(--border-color);
  scroll-margin-top: 1rem;
}

main .anchor-link:target {
  scroll-margin-top: 1rem;
}

/* 4. Links */
a {
  color: var(--link-color);
  transition: color 0.3s ease;
  text-decoration: underline;
  text-underline-offset: 0.2em; /* Make sure it doesn't overlap with underscores in a method name. */
}

a:hover {
  color: var(--link-hover-color);
}

a code:hover {
  color: var(--link-hover-color);
}

/* 5. Code and Pre */
code,
pre {
  font-family: var(--font-code);
  background-color: var(--code-block-background-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  font-size: 15px;
  line-height: 1.5;
  margin: 1em 0;
}

code {
  background-color: var(--code-block-background-color);
  padding: 0.1em 0.3em;
  border-radius: 3px;
  font-size: 85%;
}

/* Tables */
table {
  margin: 0;
  border-spacing: 0;
  border-collapse: collapse;
}

table tr th, table tr td {
  padding: 0.2em 0.4em;
  border: 1px solid var(--border-color);
}

table tr th {
  background-color: var(--table-header-background-color);
}

table tr:nth-child(even) td {
  background-color: var(--table-td-background-color);
}

/* 7. Navigation and Sidebar */
nav {
  font-family: var(--font-heading);
  font-size: 16px;
  border-right: 1px solid var(--border-color);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  width: var(--sidebar-width);
  background: var(--background-color); /* It needs an explicit background for toggling narrow screens */
  overflow-y: auto;
  z-index: 10;
  display: flex;
  flex-direction: column;
  color: var(--text-color);
}

nav[hidden] {
  display: none;
}

nav footer {
  padding: 1em;
  border-top: 1px solid var(--border-color);
}

nav footer a {
  color: var(--secondary-highlight-color);
}

nav .nav-section {
  margin-top: 1em;
  padding: 0 1em;
}

nav h2, nav h3 {
  margin: 0 0 0.5em;
  padding: 0.5em 0;
  color: var(--highlight-color);
  border-bottom: 1px solid var(--border-color);
}

nav h2 {
  font-size: 1.2em;
}

nav h3,
#table-of-contents-navigation {
  font-size: 1em;
}

ol.breadcrumb {
  display: flex;

  padding: 0;
  margin: 0 0 1em;
}

ol.breadcrumb li {
  display: block;
  list-style: none;
  font-size: 125%;
}

nav ul,
nav dl,
nav p {
  padding: 0;
  list-style: none;
  margin: 0.5em 0;
}

nav ul li {
  margin-bottom: 0.3em;
}

nav ul ul {
  padding-left: 1em;
}

nav ul ul ul {
  padding-left: 1em;
}

nav ul ul ul ul {
  padding-left: 1em;
}

nav a {
  color: var(--link-color);
  text-decoration: none;
}

nav a:hover {
  color: var(--link-hover-color);
  text-decoration: underline;
}

#navigation-toggle {
  z-index: 1000;
  font-size: 2em;
  display: block;
  position: fixed;
  top: 10px;
  left: 20px;
  cursor: pointer;
}

#navigation-toggle[aria-expanded="true"] {
  top: 10px;
  left: 250px;
}

nav ul li details {
  position: relative;
  padding-right: 1.5em;  /* Add space for the marker on the right */
}

nav ul li details > summary {
  list-style: none;  /* Remove the default marker */
  position: relative; /* So that the open/close triangle can position itself absolutely inside */
}

nav ul li details > summary::-webkit-details-marker {
  display: none;  /* Removes the default marker, in Safari 18. */
}

nav ul li details > summary::after {
  content: '▶';  /* Unicode right-pointing triangle */
  position: absolute;
  font-size: 0.8em;
  bottom: 0.1em;
  margin-left: 0.3em;
  transition: transform 0.2s ease;
}

nav ul li details[open] > summary::after {
  transform: rotate(90deg);  /* Rotate the triangle when open */
}

/* 8. Main Content */
main {
  flex: 1;
  display: block;
  margin: 3em auto;
  padding: 0 2em;
  max-width: 800px;
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-color);
  box-sizing: border-box;
}

@media (min-width: 1024px) {
  main {
    margin-left: var(--sidebar-width);
  }

  .table-of-contents main {
    margin-left: 20em;
  }

  #navigation-toggle {
    display: none;
  }
}

main h1[class] {
  margin-top: 0;
  margin-bottom: 1em;
  font-size: 2.5em;
  color: var(--highlight-color);
}

main h1,
main h2,
main h3,
main h4,
main h5,
main h6 {
  font-family: var(--font-heading);
  color: var(--highlight-color);
}

/* Search */
#search-section {
  padding: 1em;
  background-color: var(--background-color);
  border-bottom: 1px solid var(--border-color);
}

#search-field-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

#search-field {
  width: 100%;
  padding: 0.5em 1em 0.5em 2.5em;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s ease;
  color: var(--text-color);
}

#search-field:focus {
  border-color: var(--highlight-color);
}

#search-field::placeholder {
  color: var(--text-color);
}

#search-field-wrapper::before {
  content: "\1F50D";
  position: absolute;
  left: 0.75em;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: var(--text-color);
  opacity: 0.6;
}

/* Search Results */
#search-results {
  font-family: var(--font-primary);
  font-weight: 300;
}

#search-results .search-match {
  font-family: var(--font-heading);
  font-weight: normal;
}

#search-results .search-selected {
  background: var(--code-block-background-color);
  border-bottom: 1px solid transparent;
}

#search-results li {
  list-style: none;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 0.5em;
}

#search-results li:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

#search-results li p {
  padding: 0;
  margin: 0.5em;
}

#search-results .search-namespace {
  font-weight: bold;
}

#search-results li em {
  background-color: rgba(224, 108, 117, 0.1);
  font-style: normal;
}

#search-results pre {
  margin: 0.5em;
  font-family: var(--font-code);
}

/* Syntax Highlighting - Gruvbox Light Scheme */

.ruby-constant   { color: #AF3A03; }  /* Dark Orange */
.ruby-keyword    { color: #9D0006; }  /* Dark Red */
.ruby-ivar       { color: #B57614; }  /* Brown */
.ruby-operator   { color: #427B58; }  /* Dark Teal */
.ruby-identifier { color: #076678; }  /* Deep Teal */
.ruby-node       { color: #8F3F71; }  /* Plum */
.ruby-comment    { color: #928374; font-style: italic; }  /* Gray */
.ruby-regexp     { color: #8F3F71; }  /* Plum */
.ruby-value      { color: #AF3A03; }  /* Dark Orange */
.ruby-string     { color: #79740E; }  /* Olive */

/* Emphasis */
em {
  text-decoration-color: rgba(52, 48, 64, 0.25);
  text-decoration-line: underline;
  text-decoration-style: dotted;
}

strong,
em {
  color: var(--highlight-color);
  background-color: rgba(255, 111, 97, 0.1); /* Light red background for emphasis */
}

/* Paragraphs */
main p {
  line-height: 1.5em;
  font-weight: 400;
}

/* Preformatted Text */
main pre {
  margin: 1.2em 0.5em;
  padding: 1em;
  font-size: 0.8em;
}

/* Horizontal Rules */
main hr {
  margin: 1.5em 1em;
  border: 2px solid var(--border-color);
}

/* Blockquotes */
main blockquote {
  margin: 0 2em 1.2em 1.2em;
  padding-left: 0.5em;
  border-left: 2px solid var(--border-color);
}

/* Lists */
main li > p {
  margin: 0.5em;
}

/* Definition Lists */
main dl {
  margin: 1em 0.5em;
}

main dt {
  line-height: 1.5; /* matches `main p` */
  font-weight: bold;
}

main dl.note-list dt {
  margin-right: 1em;
  float: left;
}

main dl.note-list dt:has(+ dt) {
  margin-right: 0.25em;
}

main dl.note-list dt:has(+ dt)::after {
  content: ', ';
  font-weight: normal;
}

main dd {
  margin: 0 0 1em 1em;
}

main dd p:first-child {
  margin-top: 0;
}

/* Headers within Main */
main header h2 {
  margin-top: 2em;
  border-width: 0;
  border-top: 4px solid var(--border-color);
  font-size: 130%;
}

main header h3 {
  margin: 2em 0 1.5em;
  border-width: 0;
  border-top: 3px solid var(--border-color);
  font-size: 120%;
}

/* Utility Classes */
.hide { display: none !important; }
.initially-hidden { display: none; }

/* Table of Contents */
.table-of-contents ul {
  margin: 1em;
  list-style: none;
}

.table-of-contents ul ul {
  margin-top: 0.25em;
}

.table-of-contents ul :link,
.table-of-contents ul :visited {
  font-size: 16px;
}

.table-of-contents li {
  margin-bottom: 0.25em;
}

/* Method Details */
main .method-source-code {
  visibility: hidden;
  max-height: 0;
  overflow: auto;
  transition-duration: 200ms;
  transition-delay: 0ms;
  transition-property: all;
  transition-timing-function: ease-in-out;
}

main .method-source-code pre {
  border-color: var(--source-code-toggle-color);
}

main .method-source-code.active-menu {
  visibility: visible;
  max-height: 100vh;
}

main .method-description .method-calls-super {
  color: var(--text-color);
  font-weight: bold;
}

main .method-detail {
  margin-bottom: 2.5em;
}

main .method-detail:target {
  margin-left: -10px;
  border-left: 10px solid var(--border-color);
}

main .method-header {
  display: inline-block;
}

main .method-heading {
  position: relative;
  font-family: var(--font-code);
  font-size: 110%;
  font-weight: bold;
}

main .method-heading::after {
  content: '¶';
  position: absolute;
  visibility: hidden;
  color: var(--highlight-color);
  font-size: 0.5em;
}

main .method-heading:hover::after {
  visibility: visible;
}

main .method-controls {
  line-height: 20px;
  float: right;
  color: var(--source-code-toggle-color);
  cursor: pointer;
}

main .method-description,
main .aliases {
  margin-top: 0.75em;
  color: var(--text-color);
}

main .aliases {
  padding-top: 4px;
  font-style: italic;
  cursor: default;
}

main .aliases a {
  color: var(--secondary-highlight-color);
}

main .mixin-from {
  font-size: 80%;
  font-style: italic;
  margin-bottom: 0.75em;
}

main .method-description ul {
  margin-left: 1.5em;
}

main #attribute-method-details .method-detail:hover {
  background-color: transparent;
  cursor: default;
}

main .attribute-access-type {
  text-transform: uppercase;
}

/* Responsive Adjustments */
@media (max-width: 480px) {
  nav {
    width: 100%;
  }

  main {
    margin: 1em auto;
    padding: 0 1em;
    max-width: 100%;
  }

  #navigation-toggle {
    right: 10px;
    left: auto;
  }

  #navigation-toggle[aria-expanded="true"] {
    left: auto;
  }

  table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  main .method-controls {
    margin-top: 10px;
    float: none;
  }
}

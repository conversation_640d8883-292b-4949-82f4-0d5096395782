# frozen_string_literal: true
#
# DO NOT MODIFY!!!!
# This file is automatically generated by Racc 1.8.1
# from Racc grammar file "inline_parser.ry".
#

###### racc/parser.rb begin
unless $".find {|p| p.end_with?('/racc/parser.rb')}
$".push "#{__dir__}/racc/parser.rb"
#--
# Copyright (c) 1999-2006 Minero Aoki
#
# This program is free software.
# You can distribute/modify this program under the same terms of ruby.
#
# As a special exception, when this code is copied by <PERSON>cc
# into a Racc output file, you may use that output file
# without restriction.
#++

unless $".find {|p| p.end_with?('/racc/info.rb')}
$".push "#{__dir__}/racc/info.rb"

module Racc
  VERSION   = '1.8.1'
  Version = VERSION
  Copyright = 'Copyright (c) 1999-2006 Minero Aoki'
end

end


module Racc
  class ParseError < StandardError; end
end
unless defined?(::ParseError)
  ParseError = Racc::ParseError # :nodoc:
end

# Racc is an LALR(1) parser generator.
# It is written in Ruby itself, and generates Ruby programs.
#
# == Command-line Reference
#
#     racc [-o<var>filename</var>] [--output-file=<var>filename</var>]
#          [-e<var>rubypath</var>] [--executable=<var>rubypath</var>]
#          [-v] [--verbose]
#          [-O<var>filename</var>] [--log-file=<var>filename</var>]
#          [-g] [--debug]
#          [-E] [--embedded]
#          [-l] [--no-line-convert]
#          [-c] [--line-convert-all]
#          [-a] [--no-omit-actions]
#          [-C] [--check-only]
#          [-S] [--output-status]
#          [--version] [--copyright] [--help] <var>grammarfile</var>
#
# [+grammarfile+]
#   Racc grammar file. Any extension is permitted.
# [-o+outfile+, --output-file=+outfile+]
#   A filename for output. default is <+filename+>.tab.rb
# [-O+filename+, --log-file=+filename+]
#   Place logging output in file +filename+.
#   Default log file name is <+filename+>.output.
# [-e+rubypath+, --executable=+rubypath+]
#   output executable file(mode 755). where +path+ is the Ruby interpreter.
# [-v, --verbose]
#   verbose mode. create +filename+.output file, like yacc's y.output file.
# [-g, --debug]
#   add debug code to parser class. To display debugging information,
#   use this '-g' option and set @yydebug true in parser class.
# [-E, --embedded]
#   Output parser which doesn't need runtime files (racc/parser.rb).
# [-F, --frozen]
#   Output parser which declares frozen_string_literals: true
# [-C, --check-only]
#   Check syntax of racc grammar file and quit.
# [-S, --output-status]
#   Print messages time to time while compiling.
# [-l, --no-line-convert]
#   turns off line number converting.
# [-c, --line-convert-all]
#   Convert line number of actions, inner, header and footer.
# [-a, --no-omit-actions]
#   Call all actions, even if an action is empty.
# [--version]
#   print Racc version and quit.
# [--copyright]
#   Print copyright and quit.
# [--help]
#   Print usage and quit.
#
# == Generating Parser Using Racc
#
# To compile Racc grammar file, simply type:
#
#   $ racc parse.y
#
# This creates Ruby script file "parse.tab.y". The -o option can change the output filename.
#
# == Writing A Racc Grammar File
#
# If you want your own parser, you have to write a grammar file.
# A grammar file contains the name of your parser class, grammar for the parser,
# user code, and anything else.
# When writing a grammar file, yacc's knowledge is helpful.
# If you have not used yacc before, Racc is not too difficult.
#
# Here's an example Racc grammar file.
#
#   class Calcparser
#   rule
#     target: exp { print val[0] }
#
#     exp: exp '+' exp
#        | exp '*' exp
#        | '(' exp ')'
#        | NUMBER
#   end
#
# Racc grammar files resemble yacc files.
# But (of course), this is Ruby code.
# yacc's $$ is the 'result', $0, $1... is
# an array called 'val', and $-1, $-2... is an array called '_values'.
#
# See the {Grammar File Reference}[rdoc-ref:lib/racc/rdoc/grammar.en.rdoc] for
# more information on grammar files.
#
# == Parser
#
# Then you must prepare the parse entry method. There are two types of
# parse methods in Racc, Racc::Parser#do_parse and Racc::Parser#yyparse
#
# Racc::Parser#do_parse is simple.
#
# It's yyparse() of yacc, and Racc::Parser#next_token is yylex().
# This method must returns an array like [TOKENSYMBOL, ITS_VALUE].
# EOF is [false, false].
# (TOKENSYMBOL is a Ruby symbol (taken from String#intern) by default.
# If you want to change this, see the grammar reference.
#
# Racc::Parser#yyparse is little complicated, but useful.
# It does not use Racc::Parser#next_token, instead it gets tokens from any iterator.
#
# For example, <code>yyparse(obj, :scan)</code> causes
# calling +obj#scan+, and you can return tokens by yielding them from +obj#scan+.
#
# == Debugging
#
# When debugging, "-v" or/and the "-g" option is helpful.
#
# "-v" creates verbose log file (.output).
# "-g" creates a "Verbose Parser".
# Verbose Parser prints the internal status when parsing.
# But it's _not_ automatic.
# You must use -g option and set +@yydebug+ to +true+ in order to get output.
# -g option only creates the verbose parser.
#
# === Racc reported syntax error.
#
# Isn't there too many "end"?
# grammar of racc file is changed in v0.10.
#
# Racc does not use '%' mark, while yacc uses huge number of '%' marks..
#
# === Racc reported "XXXX conflicts".
#
# Try "racc -v xxxx.y".
# It causes producing racc's internal log file, xxxx.output.
#
# === Generated parsers does not work correctly
#
# Try "racc -g xxxx.y".
# This command let racc generate "debugging parser".
# Then set @yydebug=true in your parser.
# It produces a working log of your parser.
#
# == Re-distributing Racc runtime
#
# A parser, which is created by Racc, requires the Racc runtime module;
# racc/parser.rb.
#
# Ruby 1.8.x comes with Racc runtime module,
# you need NOT distribute Racc runtime files.
#
# If you want to include the Racc runtime module with your parser.
# This can be done by using '-E' option:
#
#   $ racc -E -omyparser.rb myparser.y
#
# This command creates myparser.rb which `includes' Racc runtime.
# Only you must do is to distribute your parser file (myparser.rb).
#
# Note: parser.rb is ruby license, but your parser is not.
# Your own parser is completely yours.
module Racc

  unless defined?(Racc_No_Extensions)
    Racc_No_Extensions = false # :nodoc:
  end

  class Parser

    Racc_Runtime_Version = ::Racc::VERSION
    Racc_Runtime_Core_Version_R = ::Racc::VERSION

    begin
      if Object.const_defined?(:RUBY_ENGINE) and RUBY_ENGINE == 'jruby'
        require 'jruby'
        require 'racc/cparse-jruby.jar'
        com.headius.racc.Cparse.new.load(JRuby.runtime, false)
      else
        require 'racc/cparse'
      end

      unless new.respond_to?(:_racc_do_parse_c, true)
        raise LoadError, 'old cparse.so'
      end
      if Racc_No_Extensions
        raise LoadError, 'selecting ruby version of racc runtime core'
      end

      Racc_Main_Parsing_Routine    = :_racc_do_parse_c # :nodoc:
      Racc_YY_Parse_Method         = :_racc_yyparse_c # :nodoc:
      Racc_Runtime_Core_Version    = Racc_Runtime_Core_Version_C # :nodoc:
      Racc_Runtime_Type            = 'c' # :nodoc:
    rescue LoadError
      Racc_Main_Parsing_Routine    = :_racc_do_parse_rb
      Racc_YY_Parse_Method         = :_racc_yyparse_rb
      Racc_Runtime_Core_Version    = Racc_Runtime_Core_Version_R
      Racc_Runtime_Type            = 'ruby'
    end

    def Parser.racc_runtime_type # :nodoc:
      Racc_Runtime_Type
    end

    def _racc_setup
      @yydebug = false unless self.class::Racc_debug_parser
      @yydebug = false unless defined?(@yydebug)
      if @yydebug
        @racc_debug_out = $stderr unless defined?(@racc_debug_out)
        @racc_debug_out ||= $stderr
      end
      arg = self.class::Racc_arg
      arg[13] = true if arg.size < 14
      arg
    end

    def _racc_init_sysvars
      @racc_state  = [0]
      @racc_tstack = []
      @racc_vstack = []

      @racc_t = nil
      @racc_val = nil

      @racc_read_next = true

      @racc_user_yyerror = false
      @racc_error_status = 0
    end

    # The entry point of the parser. This method is used with #next_token.
    # If Racc wants to get token (and its value), calls next_token.
    #
    # Example:
    #     def parse
    #       @q = [[1,1],
    #             [2,2],
    #             [3,3],
    #             [false, '$']]
    #       do_parse
    #     end
    #
    #     def next_token
    #       @q.shift
    #     end
    class_eval <<~RUBY, __FILE__, __LINE__ + 1
    def do_parse
      #{Racc_Main_Parsing_Routine}(_racc_setup(), false)
    end
    RUBY

    # The method to fetch next token.
    # If you use #do_parse method, you must implement #next_token.
    #
    # The format of return value is [TOKEN_SYMBOL, VALUE].
    # +token-symbol+ is represented by Ruby's symbol by default, e.g. :IDENT
    # for 'IDENT'.  ";" (String) for ';'.
    #
    # The final symbol (End of file) must be false.
    def next_token
      raise NotImplementedError, "#{self.class}\#next_token is not defined"
    end

    def _racc_do_parse_rb(arg, in_debug)
      action_table, action_check, action_default, action_pointer,
      _,            _,            _,              _,
      _,            _,            token_table,    * = arg

      _racc_init_sysvars
      tok = act = i = nil

      catch(:racc_end_parse) {
        while true
          if i = action_pointer[@racc_state[-1]]
            if @racc_read_next
              if @racc_t != 0   # not EOF
                tok, @racc_val = next_token()
                unless tok      # EOF
                  @racc_t = 0
                else
                  @racc_t = (token_table[tok] or 1)   # error token
                end
                racc_read_token(@racc_t, tok, @racc_val) if @yydebug
                @racc_read_next = false
              end
            end
            i += @racc_t
            unless i >= 0 and
                   act = action_table[i] and
                   action_check[i] == @racc_state[-1]
              act = action_default[@racc_state[-1]]
            end
          else
            act = action_default[@racc_state[-1]]
          end
          while act = _racc_evalact(act, arg)
            ;
          end
        end
      }
    end

    # Another entry point for the parser.
    # If you use this method, you must implement RECEIVER#METHOD_ID method.
    #
    # RECEIVER#METHOD_ID is a method to get next token.
    # It must 'yield' the token, which format is [TOKEN-SYMBOL, VALUE].
    class_eval <<~RUBY, __FILE__, __LINE__ + 1
    def yyparse(recv, mid)
      #{Racc_YY_Parse_Method}(recv, mid, _racc_setup(), false)
    end
    RUBY

    def _racc_yyparse_rb(recv, mid, arg, c_debug)
      action_table, action_check, action_default, action_pointer,
      _,            _,            _,              _,
      _,            _,            token_table,    * = arg

      _racc_init_sysvars

      catch(:racc_end_parse) {
        until i = action_pointer[@racc_state[-1]]
          while act = _racc_evalact(action_default[@racc_state[-1]], arg)
            ;
          end
        end
        recv.__send__(mid) do |tok, val|
          unless tok
            @racc_t = 0
          else
            @racc_t = (token_table[tok] or 1)   # error token
          end
          @racc_val = val
          @racc_read_next = false

          i += @racc_t
          unless i >= 0 and
                 act = action_table[i] and
                 action_check[i] == @racc_state[-1]
            act = action_default[@racc_state[-1]]
          end
          while act = _racc_evalact(act, arg)
            ;
          end

          while !(i = action_pointer[@racc_state[-1]]) ||
                ! @racc_read_next ||
                @racc_t == 0  # $
            unless i and i += @racc_t and
                   i >= 0 and
                   act = action_table[i] and
                   action_check[i] == @racc_state[-1]
              act = action_default[@racc_state[-1]]
            end
            while act = _racc_evalact(act, arg)
              ;
            end
          end
        end
      }
    end

    ###
    ### common
    ###

    def _racc_evalact(act, arg)
      action_table, action_check, _, action_pointer,
      _,            _,            _, _,
      _,            _,            _, shift_n,
      reduce_n,     * = arg
      nerr = 0   # tmp

      if act > 0 and act < shift_n
        #
        # shift
        #
        if @racc_error_status > 0
          @racc_error_status -= 1 unless @racc_t <= 1 # error token or EOF
        end
        @racc_vstack.push @racc_val
        @racc_state.push act
        @racc_read_next = true
        if @yydebug
          @racc_tstack.push @racc_t
          racc_shift @racc_t, @racc_tstack, @racc_vstack
        end

      elsif act < 0 and act > -reduce_n
        #
        # reduce
        #
        code = catch(:racc_jump) {
          @racc_state.push _racc_do_reduce(arg, act)
          false
        }
        if code
          case code
          when 1 # yyerror
            @racc_user_yyerror = true   # user_yyerror
            return -reduce_n
          when 2 # yyaccept
            return shift_n
          else
            raise '[Racc Bug] unknown jump code'
          end
        end

      elsif act == shift_n
        #
        # accept
        #
        racc_accept if @yydebug
        throw :racc_end_parse, @racc_vstack[0]

      elsif act == -reduce_n
        #
        # error
        #
        case @racc_error_status
        when 0
          unless arg[21]    # user_yyerror
            nerr += 1
            on_error @racc_t, @racc_val, @racc_vstack
          end
        when 3
          if @racc_t == 0   # is $
            # We're at EOF, and another error occurred immediately after
            # attempting auto-recovery
            throw :racc_end_parse, nil
          end
          @racc_read_next = true
        end
        @racc_user_yyerror = false
        @racc_error_status = 3
        while true
          if i = action_pointer[@racc_state[-1]]
            i += 1   # error token
            if  i >= 0 and
                (act = action_table[i]) and
                action_check[i] == @racc_state[-1]
              break
            end
          end
          throw :racc_end_parse, nil if @racc_state.size <= 1
          @racc_state.pop
          @racc_vstack.pop
          if @yydebug
            @racc_tstack.pop
            racc_e_pop @racc_state, @racc_tstack, @racc_vstack
          end
        end
        return act

      else
        raise "[Racc Bug] unknown action #{act.inspect}"
      end

      racc_next_state(@racc_state[-1], @racc_state) if @yydebug

      nil
    end

    def _racc_do_reduce(arg, act)
      _,          _,            _,            _,
      goto_table, goto_check,   goto_default, goto_pointer,
      nt_base,    reduce_table, _,            _,
      _,          use_result,   * = arg

      state = @racc_state
      vstack = @racc_vstack
      tstack = @racc_tstack

      i = act * -3
      len       = reduce_table[i]
      reduce_to = reduce_table[i+1]
      method_id = reduce_table[i+2]
      void_array = []

      tmp_t = tstack[-len, len] if @yydebug
      tmp_v = vstack[-len, len]
      tstack[-len, len] = void_array if @yydebug
      vstack[-len, len] = void_array
      state[-len, len]  = void_array

      # tstack must be updated AFTER method call
      if use_result
        vstack.push __send__(method_id, tmp_v, vstack, tmp_v[0])
      else
        vstack.push __send__(method_id, tmp_v, vstack)
      end
      tstack.push reduce_to

      racc_reduce(tmp_t, reduce_to, tstack, vstack) if @yydebug

      k1 = reduce_to - nt_base
      if i = goto_pointer[k1]
        i += state[-1]
        if i >= 0 and (curstate = goto_table[i]) and goto_check[i] == k1
          return curstate
        end
      end
      goto_default[k1]
    end

    # This method is called when a parse error is found.
    #
    # ERROR_TOKEN_ID is an internal ID of token which caused error.
    # You can get string representation of this ID by calling
    # #token_to_str.
    #
    # ERROR_VALUE is a value of error token.
    #
    # value_stack is a stack of symbol values.
    # DO NOT MODIFY this object.
    #
    # This method raises ParseError by default.
    #
    # If this method returns, parsers enter "error recovering mode".
    def on_error(t, val, vstack)
      raise ParseError, sprintf("parse error on value %s (%s)",
                                val.inspect, token_to_str(t) || '?')
    end

    # Enter error recovering mode.
    # This method does not call #on_error.
    def yyerror
      throw :racc_jump, 1
    end

    # Exit parser.
    # Return value is +Symbol_Value_Stack[0]+.
    def yyaccept
      throw :racc_jump, 2
    end

    # Leave error recovering mode.
    def yyerrok
      @racc_error_status = 0
    end

    # For debugging output
    def racc_read_token(t, tok, val)
      @racc_debug_out.print 'read    '
      @racc_debug_out.print tok.inspect, '(', racc_token2str(t), ') '
      @racc_debug_out.puts val.inspect
      @racc_debug_out.puts
    end

    def racc_shift(tok, tstack, vstack)
      @racc_debug_out.puts "shift   #{racc_token2str tok}"
      racc_print_stacks tstack, vstack
      @racc_debug_out.puts
    end

    def racc_reduce(toks, sim, tstack, vstack)
      out = @racc_debug_out
      out.print 'reduce '
      if toks.empty?
        out.print ' <none>'
      else
        toks.each {|t| out.print ' ', racc_token2str(t) }
      end
      out.puts " --> #{racc_token2str(sim)}"
      racc_print_stacks tstack, vstack
      @racc_debug_out.puts
    end

    def racc_accept
      @racc_debug_out.puts 'accept'
      @racc_debug_out.puts
    end

    def racc_e_pop(state, tstack, vstack)
      @racc_debug_out.puts 'error recovering mode: pop token'
      racc_print_states state
      racc_print_stacks tstack, vstack
      @racc_debug_out.puts
    end

    def racc_next_state(curstate, state)
      @racc_debug_out.puts  "goto    #{curstate}"
      racc_print_states state
      @racc_debug_out.puts
    end

    def racc_print_stacks(t, v)
      out = @racc_debug_out
      out.print '        ['
      t.each_index do |i|
        out.print ' (', racc_token2str(t[i]), ' ', v[i].inspect, ')'
      end
      out.puts ' ]'
    end

    def racc_print_states(s)
      out = @racc_debug_out
      out.print '        ['
      s.each {|st| out.print ' ', st }
      out.puts ' ]'
    end

    def racc_token2str(tok)
      self.class::Racc_token_to_s_table[tok] or
          raise "[Racc Bug] can't convert token #{tok} to string"
    end

    # Convert internal ID of token symbol to the string.
    def token_to_str(t)
      self.class::Racc_token_to_s_table[t]
    end

  end

end

end
###### racc/parser.rb end

require 'strscan'

class RDoc::RD

##
# RD format parser for inline markup such as emphasis, links, footnotes, etc.

class InlineParser < Racc::Parser


# :stopdoc:

EM_OPEN = '((*'
EM_OPEN_RE = /\A#{Regexp.quote(EM_OPEN)}/
EM_CLOSE = '*))'
EM_CLOSE_RE = /\A#{Regexp.quote(EM_CLOSE)}/
CODE_OPEN = '(({'
CODE_OPEN_RE = /\A#{Regexp.quote(CODE_OPEN)}/
CODE_CLOSE = '}))'
CODE_CLOSE_RE = /\A#{Regexp.quote(CODE_CLOSE)}/
VAR_OPEN = '((|'
VAR_OPEN_RE = /\A#{Regexp.quote(VAR_OPEN)}/
VAR_CLOSE = '|))'
VAR_CLOSE_RE = /\A#{Regexp.quote(VAR_CLOSE)}/
KBD_OPEN = '((%'
KBD_OPEN_RE = /\A#{Regexp.quote(KBD_OPEN)}/
KBD_CLOSE = '%))'
KBD_CLOSE_RE = /\A#{Regexp.quote(KBD_CLOSE)}/
INDEX_OPEN = '((:'
INDEX_OPEN_RE = /\A#{Regexp.quote(INDEX_OPEN)}/
INDEX_CLOSE = ':))'
INDEX_CLOSE_RE = /\A#{Regexp.quote(INDEX_CLOSE)}/
REF_OPEN = '((<'
REF_OPEN_RE = /\A#{Regexp.quote(REF_OPEN)}/
REF_CLOSE = '>))'
REF_CLOSE_RE = /\A#{Regexp.quote(REF_CLOSE)}/
FOOTNOTE_OPEN = '((-'
FOOTNOTE_OPEN_RE = /\A#{Regexp.quote(FOOTNOTE_OPEN)}/
FOOTNOTE_CLOSE = '-))'
FOOTNOTE_CLOSE_RE = /\A#{Regexp.quote(FOOTNOTE_CLOSE)}/
VERB_OPEN = "(('"
VERB_OPEN_RE = /\A#{Regexp.quote(VERB_OPEN)}/
VERB_CLOSE = "'))"
VERB_CLOSE_RE = /\A#{Regexp.quote(VERB_CLOSE)}/

BAR = "|"
BAR_RE = /\A#{Regexp.quote(BAR)}/
QUOTE = '"'
QUOTE_RE = /\A#{Regexp.quote(QUOTE)}/
SLASH = "/"
SLASH_RE = /\A#{Regexp.quote(SLASH)}/
BACK_SLASH = "\\"
BACK_SLASH_RE = /\A#{Regexp.quote(BACK_SLASH)}/
URL = "URL:"
URL_RE = /\A#{Regexp.quote(URL)}/

other_re_mode = Regexp::EXTENDED
other_re_mode |= Regexp::MULTILINE

OTHER_RE = Regexp.new(
  "\\A.+?(?=#{Regexp.quote(EM_OPEN)}|#{Regexp.quote(EM_CLOSE)}|
              #{Regexp.quote(CODE_OPEN)}|#{Regexp.quote(CODE_CLOSE)}|
              #{Regexp.quote(VAR_OPEN)}|#{Regexp.quote(VAR_CLOSE)}|
              #{Regexp.quote(KBD_OPEN)}|#{Regexp.quote(KBD_CLOSE)}|
              #{Regexp.quote(INDEX_OPEN)}|#{Regexp.quote(INDEX_CLOSE)}|
              #{Regexp.quote(REF_OPEN)}|#{Regexp.quote(REF_CLOSE)}|
            #{Regexp.quote(FOOTNOTE_OPEN)}|#{Regexp.quote(FOOTNOTE_CLOSE)}|
              #{Regexp.quote(VERB_OPEN)}|#{Regexp.quote(VERB_CLOSE)}|
              #{Regexp.quote(BAR)}|
              #{Regexp.quote(QUOTE)}|
              #{Regexp.quote(SLASH)}|
              #{Regexp.quote(BACK_SLASH)}|
              #{Regexp.quote(URL)})", other_re_mode)

# :startdoc:

##
# Creates a new parser for inline markup in the rd format.  The +block_parser+
# is used to for footnotes and labels in the inline text.

def initialize block_parser
  @block_parser = block_parser
end

##
# Parses the +inline+ text from RD format into RDoc format.

def parse inline
  @inline = inline
  @src = StringScanner.new inline
  @pre = "".dup
  @yydebug = true
  do_parse.to_s
end

##
# Returns the next token from the inline text

def next_token
  return [false, false] if @src.eos?
#  p @src.rest if @yydebug
  if ret = @src.scan(EM_OPEN_RE)
    @pre << ret
    [:EM_OPEN, ret]
  elsif ret = @src.scan(EM_CLOSE_RE)
    @pre << ret
    [:EM_CLOSE, ret]
  elsif ret = @src.scan(CODE_OPEN_RE)
    @pre << ret
    [:CODE_OPEN, ret]
  elsif ret = @src.scan(CODE_CLOSE_RE)
    @pre << ret
    [:CODE_CLOSE, ret]
  elsif ret = @src.scan(VAR_OPEN_RE)
    @pre << ret
    [:VAR_OPEN, ret]
  elsif ret = @src.scan(VAR_CLOSE_RE)
    @pre << ret
    [:VAR_CLOSE, ret]
  elsif ret = @src.scan(KBD_OPEN_RE)
    @pre << ret
    [:KBD_OPEN, ret]
  elsif ret = @src.scan(KBD_CLOSE_RE)
    @pre << ret
    [:KBD_CLOSE, ret]
  elsif ret = @src.scan(INDEX_OPEN_RE)
    @pre << ret
    [:INDEX_OPEN, ret]
  elsif ret = @src.scan(INDEX_CLOSE_RE)
    @pre << ret
    [:INDEX_CLOSE, ret]
  elsif ret = @src.scan(REF_OPEN_RE)
    @pre << ret
    [:REF_OPEN, ret]
  elsif ret = @src.scan(REF_CLOSE_RE)
    @pre << ret
    [:REF_CLOSE, ret]
  elsif ret = @src.scan(FOOTNOTE_OPEN_RE)
    @pre << ret
    [:FOOTNOTE_OPEN, ret]
  elsif ret = @src.scan(FOOTNOTE_CLOSE_RE)
    @pre << ret
    [:FOOTNOTE_CLOSE, ret]
  elsif ret = @src.scan(VERB_OPEN_RE)
    @pre << ret
    [:VERB_OPEN, ret]
  elsif ret = @src.scan(VERB_CLOSE_RE)
    @pre << ret
    [:VERB_CLOSE, ret]
  elsif ret = @src.scan(BAR_RE)
    @pre << ret
    [:BAR, ret]
  elsif ret = @src.scan(QUOTE_RE)
    @pre << ret
    [:QUOTE, ret]
  elsif ret = @src.scan(SLASH_RE)
    @pre << ret
    [:SLASH, ret]
  elsif ret = @src.scan(BACK_SLASH_RE)
    @pre << ret
    [:BACK_SLASH, ret]
  elsif ret = @src.scan(URL_RE)
    @pre << ret
    [:URL, ret]
  elsif ret = @src.scan(OTHER_RE)
    @pre << ret
    [:OTHER, ret]
  else
    ret = @src.rest
    @pre << ret
    @src.terminate
    [:OTHER, ret]
  end
end

##
# Raises a ParseError when invalid formatting is found

def on_error(et, ev, values)
  lines_of_rest = @src.rest.lines.to_a.length
  prev_words = prev_words_on_error(ev)
  at = 4 + prev_words.length

  message = <<-MSG
RD syntax error: line #{@block_parser.line_index - lines_of_rest}:
...#{prev_words} #{(ev||'')} #{next_words_on_error()} ...
  MSG

  message << " " * at + "^" * (ev ? ev.length : 0) + "\n"
  raise ParseError, message
end

##
# Returns words before the error

def prev_words_on_error(ev)
  pre = @pre
  if ev and /#{Regexp.quote(ev)}$/ =~ pre
    pre = $`
  end
  last_line(pre)
end

##
# Returns the last line of +src+

def last_line(src)
  if n = src.rindex("\n")
    src[(n+1) .. -1]
  else
    src
  end
end
private :last_line

##
# Returns words following an error

def next_words_on_error
  if n = @src.rest.index("\n")
    @src.rest[0 .. (n-1)]
  else
    @src.rest
  end
end

##
# Creates a new RDoc::RD::Inline for the +rdoc+ markup and the raw +reference+

def inline rdoc, reference = rdoc
  RDoc::RD::Inline.new rdoc, reference
end

# :stopdoc:
##### State transition tables begin ###

racc_action_table = [
   104,   103,   102,   100,   101,    99,   115,   116,   117,    29,
   105,   106,   107,   108,   109,   110,   111,   112,   113,   114,
    84,   118,   119,    63,    64,    65,    61,    81,    62,    76,
    78,    79,    85,    66,    67,    68,    69,    70,    71,    72,
    73,    74,    75,    77,    80,   149,    63,    64,    65,   153,
    81,    62,    76,    78,    79,    86,    66,    67,    68,    69,
    70,    71,    72,    73,    74,    75,    77,    80,   152,   104,
   103,   102,   100,   101,    99,   115,   116,   117,    87,   105,
   106,   107,   108,   109,   110,   111,   112,   113,   114,    88,
   118,   119,   104,   103,   102,   100,   101,    99,   115,   116,
   117,    89,   105,   106,   107,   108,   109,   110,   111,   112,
   113,   114,    96,   118,   119,   104,   103,   102,   100,   101,
    99,   115,   116,   117,   124,   105,   106,   107,   108,   109,
   110,   111,   112,   113,   114,   137,   118,   119,    22,    23,
    24,    25,    26,    21,    18,    19,   176,   177,    13,   148,
    14,   154,    15,   137,    16,   161,    17,   164,   173,    20,
    22,    23,    24,    25,    26,    21,    18,    19,   175,   177,
    13,   nil,    14,   nil,    15,   nil,    16,   nil,    17,   nil,
   nil,    20,    22,    23,    24,    25,    26,    21,    18,    19,
   nil,   nil,    13,   nil,    14,   nil,    15,   nil,    16,   nil,
    17,   nil,   nil,    20,    22,    23,    24,    25,    26,    21,
    18,    19,   nil,   nil,    13,   nil,    14,   nil,    15,   nil,
    16,   nil,    17,   nil,   nil,    20,    22,    23,    24,    25,
    26,    21,    18,    19,   nil,   nil,    13,   nil,    14,   nil,
    15,   nil,    16,   nil,    17,   nil,   nil,    20,    22,    23,
    24,    25,    26,    21,    18,    19,   nil,   nil,    13,   nil,
    14,   nil,    15,   nil,    16,   nil,    17,   nil,   nil,    20,
    22,    23,    24,    25,    26,    21,    18,    19,   nil,   nil,
    13,   nil,    14,   nil,    15,   nil,    16,   nil,    17,    42,
   nil,    20,    54,    38,    53,    55,    56,    57,   nil,    13,
   nil,    14,   nil,    15,   nil,    16,   nil,    17,   nil,   nil,
    20,    22,    23,    24,    25,    26,    21,    18,    19,   nil,
   nil,    13,   nil,    14,   nil,    15,   nil,    16,   nil,    17,
   nil,   nil,    20,    63,    64,    65,    61,    81,    62,    76,
    78,    79,   nil,    66,    67,    68,    69,    70,    71,    72,
    73,    74,    75,    77,    80,   122,   nil,   nil,    54,   nil,
    53,    55,    56,    57,   nil,    13,   nil,    14,   nil,    15,
   nil,    16,   nil,    17,   145,   nil,    20,    54,   133,    53,
    55,    56,    57,   nil,    13,   nil,    14,   nil,    15,   nil,
    16,   nil,    17,   145,   nil,    20,    54,   133,    53,    55,
    56,    57,   nil,    13,   nil,    14,   nil,    15,   nil,    16,
   nil,    17,   145,   nil,    20,    54,   133,    53,    55,    56,
    57,   nil,    13,   nil,    14,   nil,    15,   nil,    16,   nil,
    17,   145,   nil,    20,    54,   133,    53,    55,    56,    57,
   nil,    13,   nil,    14,   nil,    15,   nil,    16,   nil,    17,
   nil,   nil,    20,   135,   136,    54,   133,    53,    55,    56,
    57,   nil,    13,   nil,    14,   nil,    15,   nil,    16,   nil,
    17,   nil,   nil,    20,   135,   136,    54,   133,    53,    55,
    56,    57,   nil,    13,   nil,    14,   nil,    15,   nil,    16,
   nil,    17,   nil,   nil,    20,   135,   136,    54,   133,    53,
    55,    56,    57,   nil,    13,   nil,    14,   nil,    15,   nil,
    16,   nil,    17,    95,   nil,    20,    54,    91,    53,    55,
    56,    57,   145,   nil,   nil,    54,   133,    53,    55,    56,
    57,   158,   nil,   nil,    54,   nil,    53,    55,    56,    57,
   165,   135,   136,    54,   133,    53,    55,    56,    57,   145,
   nil,   nil,    54,   133,    53,    55,    56,    57,   172,   135,
   136,    54,   133,    53,    55,    56,    57,   174,   135,   136,
    54,   133,    53,    55,    56,    57,   178,   135,   136,    54,
   133,    53,    55,    56,    57,   135,   136,    54,   133,    53,
    55,    56,    57,   135,   136,    54,   133,    53,    55,    56,
    57,   135,   136,    54,   133,    53,    55,    56,    57,    22,
    23,    24,    25,    26,    21 ]

racc_action_check = [
    38,    38,    38,    38,    38,    38,    38,    38,    38,     1,
    38,    38,    38,    38,    38,    38,    38,    38,    38,    38,
    29,    38,    38,    59,    59,    59,    59,    59,    59,    59,
    59,    59,    31,    59,    59,    59,    59,    59,    59,    59,
    59,    59,    59,    59,    59,    59,    61,    61,    61,    61,
    61,    61,    61,    61,    61,    32,    61,    61,    61,    61,
    61,    61,    61,    61,    61,    61,    61,    61,    61,    91,
    91,    91,    91,    91,    91,    91,    91,    91,    33,    91,
    91,    91,    91,    91,    91,    91,    91,    91,    91,    34,
    91,    91,    97,    97,    97,    97,    97,    97,    97,    97,
    97,    35,    97,    97,    97,    97,    97,    97,    97,    97,
    97,    97,    37,    97,    97,   155,   155,   155,   155,   155,
   155,   155,   155,   155,    41,   155,   155,   155,   155,   155,
   155,   155,   155,   155,   155,    43,   155,   155,     0,     0,
     0,     0,     0,     0,     0,     0,   165,   165,     0,    58,
     0,    90,     0,    94,     0,   100,     0,   125,   162,     0,
     2,     2,     2,     2,     2,     2,     2,     2,   164,   172,
     2,   nil,     2,   nil,     2,   nil,     2,   nil,     2,   nil,
   nil,     2,    13,    13,    13,    13,    13,    13,    13,    13,
   nil,   nil,    13,   nil,    13,   nil,    13,   nil,    13,   nil,
    13,   nil,   nil,    13,    14,    14,    14,    14,    14,    14,
    14,    14,   nil,   nil,    14,   nil,    14,   nil,    14,   nil,
    14,   nil,    14,   nil,   nil,    14,    15,    15,    15,    15,
    15,    15,    15,    15,   nil,   nil,    15,   nil,    15,   nil,
    15,   nil,    15,   nil,    15,   nil,   nil,    15,    16,    16,
    16,    16,    16,    16,    16,    16,   nil,   nil,    16,   nil,
    16,   nil,    16,   nil,    16,   nil,    16,   nil,   nil,    16,
    17,    17,    17,    17,    17,    17,    17,    17,   nil,   nil,
    17,   nil,    17,   nil,    17,   nil,    17,   nil,    17,    18,
   nil,    17,    18,    18,    18,    18,    18,    18,   nil,    18,
   nil,    18,   nil,    18,   nil,    18,   nil,    18,   nil,   nil,
    18,    19,    19,    19,    19,    19,    19,    19,    19,   nil,
   nil,    19,   nil,    19,   nil,    19,   nil,    19,   nil,    19,
   nil,   nil,    19,    20,    20,    20,    20,    20,    20,    20,
    20,    20,   nil,    20,    20,    20,    20,    20,    20,    20,
    20,    20,    20,    20,    20,    39,   nil,   nil,    39,   nil,
    39,    39,    39,    39,   nil,    39,   nil,    39,   nil,    39,
   nil,    39,   nil,    39,    44,   nil,    39,    44,    44,    44,
    44,    44,    44,   nil,    44,   nil,    44,   nil,    44,   nil,
    44,   nil,    44,    45,   nil,    44,    45,    45,    45,    45,
    45,    45,   nil,    45,   nil,    45,   nil,    45,   nil,    45,
   nil,    45,   138,   nil,    45,   138,   138,   138,   138,   138,
   138,   nil,   138,   nil,   138,   nil,   138,   nil,   138,   nil,
   138,   146,   nil,   138,   146,   146,   146,   146,   146,   146,
   nil,   146,   nil,   146,   nil,   146,   nil,   146,   nil,   146,
   nil,   nil,   146,    42,    42,    42,    42,    42,    42,    42,
    42,   nil,    42,   nil,    42,   nil,    42,   nil,    42,   nil,
    42,   nil,   nil,    42,   122,   122,   122,   122,   122,   122,
   122,   122,   nil,   122,   nil,   122,   nil,   122,   nil,   122,
   nil,   122,   nil,   nil,   122,   127,   127,   127,   127,   127,
   127,   127,   127,   nil,   127,   nil,   127,   nil,   127,   nil,
   127,   nil,   127,    36,   nil,   127,    36,    36,    36,    36,
    36,    36,    52,   nil,   nil,    52,    52,    52,    52,    52,
    52,    92,   nil,   nil,    92,   nil,    92,    92,    92,    92,
   126,   126,   126,   126,   126,   126,   126,   126,   126,   142,
   nil,   nil,   142,   142,   142,   142,   142,   142,   159,   159,
   159,   159,   159,   159,   159,   159,   159,   163,   163,   163,
   163,   163,   163,   163,   163,   163,   171,   171,   171,   171,
   171,   171,   171,   171,   171,    95,    95,    95,    95,    95,
    95,    95,    95,   158,   158,   158,   158,   158,   158,   158,
   158,   168,   168,   168,   168,   168,   168,   168,   168,    27,
    27,    27,    27,    27,    27 ]

racc_action_pointer = [
   135,     9,   157,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   179,   201,   223,   245,   267,   286,   308,
   330,   nil,   nil,   nil,   nil,   nil,   nil,   606,   nil,    20,
   nil,    18,    39,    60,    69,    79,   510,    89,    -3,   352,
   nil,   120,   449,   130,   371,   390,   nil,   nil,   nil,   nil,
   nil,   nil,   519,   nil,   nil,   nil,   nil,   nil,   138,    20,
   nil,    43,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   128,    66,   528,   nil,   148,   581,   nil,    89,   nil,   nil,
   149,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   470,   nil,   nil,   154,   537,   491,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   nil,   nil,   nil,   409,   nil,
   nil,   nil,   546,   nil,   nil,   nil,   428,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   nil,   112,   nil,   nil,   589,   555,
   nil,   nil,   155,   564,   164,   142,   nil,   nil,   597,   nil,
   nil,   573,   164,   nil,   nil,   nil,   nil,   nil,   nil ]

racc_action_default = [
  -138,  -138,    -1,    -3,    -4,    -5,    -6,    -7,    -8,    -9,
   -10,   -11,   -12,  -138,  -138,  -138,  -138,  -138,  -138,  -138,
  -138,  -103,  -104,  -105,  -106,  -107,  -108,  -111,  -110,  -138,
    -2,  -138,  -138,  -138,  -138,  -138,  -138,  -138,  -138,   -27,
   -26,   -35,  -138,   -58,   -41,   -40,   -47,   -48,   -49,   -50,
   -51,   -52,   -63,   -66,   -67,   -68,   -69,   -70,  -138,  -138,
  -112,  -138,  -116,  -117,  -118,  -119,  -120,  -121,  -122,  -123,
  -124,  -125,  -126,  -127,  -128,  -129,  -130,  -131,  -132,  -133,
  -134,  -135,  -137,  -109,   179,   -13,   -14,   -15,   -16,   -17,
  -138,  -138,   -23,   -22,   -33,  -138,   -19,   -24,   -79,   -80,
  -138,   -82,   -83,   -84,   -85,   -86,   -87,   -88,   -89,   -90,
   -91,   -92,   -93,   -94,   -95,   -96,   -97,   -98,   -99,  -100,
   -25,   -35,  -138,   -58,   -28,  -138,   -59,   -42,   -46,   -55,
   -56,   -65,   -71,   -72,   -75,   -76,   -77,   -31,   -38,   -44,
   -53,   -54,   -57,   -61,   -73,   -74,   -39,   -62,  -101,  -102,
  -136,  -113,  -114,  -115,   -18,   -20,   -21,   -33,  -138,  -138,
   -78,   -81,  -138,   -59,   -36,   -37,   -64,   -45,   -59,   -43,
   -60,  -138,   -34,   -36,   -37,   -29,   -30,   -32,   -34 ]

racc_goto_table = [
   126,    44,   125,    43,   144,   144,   160,    93,    97,    52,
   166,    82,   144,    40,    41,    39,   138,   146,   169,    30,
    36,    94,    44,     1,   123,   129,   169,    52,    90,    37,
    52,   167,   147,    92,   120,   121,    31,    32,    33,    34,
    35,   170,    58,   166,    59,    83,   170,   166,   151,   nil,
   150,   nil,   166,   159,     4,   166,     4,   nil,   nil,   nil,
   nil,   155,   nil,   156,   160,   nil,   nil,     4,     4,     4,
     4,     4,   nil,     4,     5,   nil,     5,   157,   nil,   nil,
   163,   nil,   162,    52,   nil,   168,   nil,     5,     5,     5,
     5,     5,   nil,     5,   nil,   nil,   nil,   nil,   144,   nil,
   nil,   nil,   144,   nil,   nil,   129,   144,   144,   nil,     6,
   129,     6,   nil,   nil,   nil,   nil,   171,     7,   nil,     7,
   nil,   nil,     6,     6,     6,     6,     6,     8,     6,     8,
     7,     7,     7,     7,     7,    11,     7,    11,   nil,   nil,
     8,     8,     8,     8,     8,   nil,     8,   nil,    11,    11,
    11,    11,    11,   nil,    11 ]

racc_goto_check = [
    22,    24,    21,    23,    36,    36,    37,    18,    16,    34,
    35,    41,    36,    19,    20,    17,    25,    25,    28,     3,
    13,    23,    24,     1,    23,    24,    28,    34,    14,    15,
    34,    29,    32,    17,    19,    20,     1,     1,     1,     1,
     1,    33,     1,    35,    38,    39,    33,    35,    42,   nil,
    41,   nil,    35,    22,     4,    35,     4,   nil,   nil,   nil,
   nil,    16,   nil,    18,    37,   nil,   nil,     4,     4,     4,
     4,     4,   nil,     4,     5,   nil,     5,    23,   nil,   nil,
    22,   nil,    21,    34,   nil,    22,   nil,     5,     5,     5,
     5,     5,   nil,     5,   nil,   nil,   nil,   nil,    36,   nil,
   nil,   nil,    36,   nil,   nil,    24,    36,    36,   nil,     6,
    24,     6,   nil,   nil,   nil,   nil,    22,     7,   nil,     7,
   nil,   nil,     6,     6,     6,     6,     6,     8,     6,     8,
     7,     7,     7,     7,     7,    11,     7,    11,   nil,   nil,
     8,     8,     8,     8,     8,   nil,     8,   nil,    11,    11,
    11,    11,    11,   nil,    11 ]

racc_goto_pointer = [
   nil,    23,   nil,    17,    54,    74,   109,   117,   127,   nil,
   nil,   135,   nil,     2,    -8,    11,   -30,    -3,   -29,    -5,
    -4,   -40,   -42,   -15,   -17,   -28,   nil,   nil,  -120,   -96,
   nil,   nil,   -20,  -101,    -9,  -116,   -40,   -91,    24,    18,
   nil,    -9,   -13 ]

racc_goto_default = [
   nil,   nil,     2,     3,    46,    47,    48,    49,    50,     9,
    10,    51,    12,   nil,   nil,   nil,   nil,   nil,   nil,   nil,
   nil,   nil,   nil,   nil,   140,   nil,    45,   127,   139,   128,
   141,   130,   142,   143,   132,   131,   134,    98,   nil,    28,
    27,   nil,    60 ]

racc_reduce_table = [
  0, 0, :racc_error,
  1, 27, :_reduce_none,
  2, 28, :_reduce_2,
  1, 28, :_reduce_3,
  1, 29, :_reduce_none,
  1, 29, :_reduce_none,
  1, 29, :_reduce_none,
  1, 29, :_reduce_none,
  1, 29, :_reduce_none,
  1, 29, :_reduce_none,
  1, 29, :_reduce_none,
  1, 29, :_reduce_none,
  1, 29, :_reduce_none,
  3, 30, :_reduce_13,
  3, 31, :_reduce_14,
  3, 32, :_reduce_15,
  3, 33, :_reduce_16,
  3, 34, :_reduce_17,
  4, 35, :_reduce_18,
  3, 35, :_reduce_19,
  2, 40, :_reduce_20,
  2, 40, :_reduce_21,
  1, 40, :_reduce_22,
  1, 40, :_reduce_23,
  2, 41, :_reduce_24,
  2, 41, :_reduce_25,
  1, 41, :_reduce_26,
  1, 41, :_reduce_27,
  2, 39, :_reduce_none,
  4, 39, :_reduce_29,
  4, 39, :_reduce_30,
  2, 43, :_reduce_31,
  4, 43, :_reduce_32,
  1, 44, :_reduce_33,
  3, 44, :_reduce_34,
  1, 45, :_reduce_none,
  3, 45, :_reduce_36,
  3, 45, :_reduce_37,
  2, 46, :_reduce_38,
  2, 46, :_reduce_39,
  1, 46, :_reduce_40,
  1, 46, :_reduce_41,
  1, 47, :_reduce_none,
  2, 51, :_reduce_43,
  1, 51, :_reduce_44,
  2, 53, :_reduce_45,
  1, 53, :_reduce_46,
  1, 50, :_reduce_none,
  1, 50, :_reduce_none,
  1, 50, :_reduce_none,
  1, 50, :_reduce_none,
  1, 50, :_reduce_none,
  1, 50, :_reduce_none,
  1, 54, :_reduce_none,
  1, 54, :_reduce_none,
  1, 55, :_reduce_none,
  1, 55, :_reduce_none,
  1, 56, :_reduce_57,
  1, 52, :_reduce_58,
  1, 57, :_reduce_59,
  2, 58, :_reduce_60,
  1, 58, :_reduce_none,
  2, 49, :_reduce_62,
  1, 49, :_reduce_none,
  2, 48, :_reduce_64,
  1, 48, :_reduce_none,
  1, 60, :_reduce_none,
  1, 60, :_reduce_none,
  1, 60, :_reduce_none,
  1, 60, :_reduce_none,
  1, 60, :_reduce_none,
  1, 62, :_reduce_none,
  1, 62, :_reduce_none,
  1, 59, :_reduce_none,
  1, 59, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  1, 61, :_reduce_none,
  2, 42, :_reduce_78,
  1, 42, :_reduce_none,
  1, 63, :_reduce_none,
  2, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  1, 63, :_reduce_none,
  3, 36, :_reduce_101,
  3, 37, :_reduce_102,
  1, 65, :_reduce_none,
  1, 65, :_reduce_none,
  1, 65, :_reduce_none,
  1, 65, :_reduce_none,
  1, 65, :_reduce_none,
  1, 65, :_reduce_none,
  2, 66, :_reduce_109,
  1, 66, :_reduce_none,
  1, 38, :_reduce_111,
  1, 67, :_reduce_none,
  2, 67, :_reduce_113,
  2, 67, :_reduce_114,
  2, 67, :_reduce_115,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  1, 68, :_reduce_none,
  2, 64, :_reduce_136,
  1, 64, :_reduce_none ]

racc_reduce_n = 138

racc_shift_n = 179

racc_token_table = {
  false => 0,
  :error => 1,
  :EX_LOW => 2,
  :QUOTE => 3,
  :BAR => 4,
  :SLASH => 5,
  :BACK_SLASH => 6,
  :URL => 7,
  :OTHER => 8,
  :REF_OPEN => 9,
  :FOOTNOTE_OPEN => 10,
  :FOOTNOTE_CLOSE => 11,
  :EX_HIGH => 12,
  :EM_OPEN => 13,
  :EM_CLOSE => 14,
  :CODE_OPEN => 15,
  :CODE_CLOSE => 16,
  :VAR_OPEN => 17,
  :VAR_CLOSE => 18,
  :KBD_OPEN => 19,
  :KBD_CLOSE => 20,
  :INDEX_OPEN => 21,
  :INDEX_CLOSE => 22,
  :REF_CLOSE => 23,
  :VERB_OPEN => 24,
  :VERB_CLOSE => 25 }

racc_nt_base = 26

racc_use_result_var = true

Racc_arg = [
  racc_action_table,
  racc_action_check,
  racc_action_default,
  racc_action_pointer,
  racc_goto_table,
  racc_goto_check,
  racc_goto_default,
  racc_goto_pointer,
  racc_nt_base,
  racc_reduce_table,
  racc_token_table,
  racc_shift_n,
  racc_reduce_n,
  racc_use_result_var ]
Ractor.make_shareable(Racc_arg) if defined?(Ractor)

Racc_token_to_s_table = [
  "$end",
  "error",
  "EX_LOW",
  "QUOTE",
  "BAR",
  "SLASH",
  "BACK_SLASH",
  "URL",
  "OTHER",
  "REF_OPEN",
  "FOOTNOTE_OPEN",
  "FOOTNOTE_CLOSE",
  "EX_HIGH",
  "EM_OPEN",
  "EM_CLOSE",
  "CODE_OPEN",
  "CODE_CLOSE",
  "VAR_OPEN",
  "VAR_CLOSE",
  "KBD_OPEN",
  "KBD_CLOSE",
  "INDEX_OPEN",
  "INDEX_CLOSE",
  "REF_CLOSE",
  "VERB_OPEN",
  "VERB_CLOSE",
  "$start",
  "content",
  "elements",
  "element",
  "emphasis",
  "code",
  "var",
  "keyboard",
  "index",
  "reference",
  "footnote",
  "verb",
  "normal_str_ele",
  "substitute",
  "ref_label",
  "ref_label2",
  "ref_url_strings",
  "filename",
  "element_label",
  "element_label2",
  "ref_subst_content",
  "ref_subst_content_q",
  "ref_subst_strings_q",
  "ref_subst_strings_first",
  "ref_subst_ele2",
  "ref_subst_eles",
  "ref_subst_str_ele_first",
  "ref_subst_eles_q",
  "ref_subst_ele",
  "ref_subst_ele_q",
  "ref_subst_str_ele",
  "ref_subst_str_ele_q",
  "ref_subst_strings",
  "ref_subst_string3",
  "ref_subst_string",
  "ref_subst_string_q",
  "ref_subst_string2",
  "ref_url_string",
  "verb_strings",
  "normal_string",
  "normal_strings",
  "verb_string",
  "verb_normal_string" ]
Ractor.make_shareable(Racc_token_to_s_table) if defined?(Ractor)

Racc_debug_parser = false

##### State transition tables end #####

# reduce 0 omitted

# reduce 1 omitted

def _reduce_2(val, _values, result)
 result.append val[1]
    result
end

def _reduce_3(val, _values, result)
 result = val[0]
    result
end

# reduce 4 omitted

# reduce 5 omitted

# reduce 6 omitted

# reduce 7 omitted

# reduce 8 omitted

# reduce 9 omitted

# reduce 10 omitted

# reduce 11 omitted

# reduce 12 omitted

def _reduce_13(val, _values, result)
      content = val[1]
      result = inline "<em>#{content}</em>", content

    result
end

def _reduce_14(val, _values, result)
      content = val[1]
      result = inline "<code>#{content}</code>", content

    result
end

def _reduce_15(val, _values, result)
      content = val[1]
      result = inline "+#{content}+", content

    result
end

def _reduce_16(val, _values, result)
      content = val[1]
      result = inline "<tt>#{content}</tt>", content

    result
end

def _reduce_17(val, _values, result)
      label = val[1]
      @block_parser.add_label label.reference
      result = "<span id=\"label-#{label}\">#{label}</span>"

    result
end

def _reduce_18(val, _values, result)
      result = "{#{val[1]}}[#{val[2].join}]"

    result
end

def _reduce_19(val, _values, result)
      scheme, inline = val[1]

      result = "{#{inline}}[#{scheme}#{inline.reference}]"

    result
end

def _reduce_20(val, _values, result)
      result = [nil, inline(val[1])]

    result
end

def _reduce_21(val, _values, result)
      result = [
        'rdoc-label:',
        inline("#{val[0].reference}/#{val[1].reference}")
      ]

    result
end

def _reduce_22(val, _values, result)
      result = ['rdoc-label:', val[0].reference]

    result
end

def _reduce_23(val, _values, result)
      result = ['rdoc-label:', "#{val[0].reference}/"]

    result
end

def _reduce_24(val, _values, result)
      result = [nil, inline(val[1])]

    result
end

def _reduce_25(val, _values, result)
      result = [
        'rdoc-label:',
        inline("#{val[0].reference}/#{val[1].reference}")
      ]

    result
end

def _reduce_26(val, _values, result)
      result = ['rdoc-label:', val[0]]

    result
end

def _reduce_27(val, _values, result)
      ref = val[0].reference
      result = ['rdoc-label:', inline(ref, "#{ref}/")]

    result
end

# reduce 28 omitted

def _reduce_29(val, _values, result)
 result = val[1]
    result
end

def _reduce_30(val, _values, result)
 result = val[1]
    result
end

def _reduce_31(val, _values, result)
      result = inline val[0]

    result
end

def _reduce_32(val, _values, result)
      result = inline "\"#{val[1]}\""

    result
end

def _reduce_33(val, _values, result)
      result = inline val[0]

    result
end

def _reduce_34(val, _values, result)
      result = inline "\"#{val[1]}\""

    result
end

# reduce 35 omitted

def _reduce_36(val, _values, result)
 result = val[1]
    result
end

def _reduce_37(val, _values, result)
 result = inline val[1]
    result
end

def _reduce_38(val, _values, result)
      result = val[0].append val[1]

    result
end

def _reduce_39(val, _values, result)
      result = val[0].append val[1]

    result
end

def _reduce_40(val, _values, result)
      result = val[0]

    result
end

def _reduce_41(val, _values, result)
      result = inline val[0]

    result
end

# reduce 42 omitted

def _reduce_43(val, _values, result)
      result = val[0].append val[1]

    result
end

def _reduce_44(val, _values, result)
      result = inline val[0]

    result
end

def _reduce_45(val, _values, result)
      result = val[0].append val[1]

    result
end

def _reduce_46(val, _values, result)
      result = val[0]

    result
end

# reduce 47 omitted

# reduce 48 omitted

# reduce 49 omitted

# reduce 50 omitted

# reduce 51 omitted

# reduce 52 omitted

# reduce 53 omitted

# reduce 54 omitted

# reduce 55 omitted

# reduce 56 omitted

def _reduce_57(val, _values, result)
      result = val[0]

    result
end

def _reduce_58(val, _values, result)
      result = inline val[0]

    result
end

def _reduce_59(val, _values, result)
      result = inline val[0]

    result
end

def _reduce_60(val, _values, result)
 result << val[1]
    result
end

# reduce 61 omitted

def _reduce_62(val, _values, result)
      result << val[1]

    result
end

# reduce 63 omitted

def _reduce_64(val, _values, result)
      result << val[1]

    result
end

# reduce 65 omitted

# reduce 66 omitted

# reduce 67 omitted

# reduce 68 omitted

# reduce 69 omitted

# reduce 70 omitted

# reduce 71 omitted

# reduce 72 omitted

# reduce 73 omitted

# reduce 74 omitted

# reduce 75 omitted

# reduce 76 omitted

# reduce 77 omitted

def _reduce_78(val, _values, result)
 result << val[1]
    result
end

# reduce 79 omitted

# reduce 80 omitted

# reduce 81 omitted

# reduce 82 omitted

# reduce 83 omitted

# reduce 84 omitted

# reduce 85 omitted

# reduce 86 omitted

# reduce 87 omitted

# reduce 88 omitted

# reduce 89 omitted

# reduce 90 omitted

# reduce 91 omitted

# reduce 92 omitted

# reduce 93 omitted

# reduce 94 omitted

# reduce 95 omitted

# reduce 96 omitted

# reduce 97 omitted

# reduce 98 omitted

# reduce 99 omitted

# reduce 100 omitted

def _reduce_101(val, _values, result)
      index = @block_parser.add_footnote val[1].rdoc
      result = "{*#{index}}[rdoc-label:foottext-#{index}:footmark-#{index}]"

    result
end

def _reduce_102(val, _values, result)
      result = inline "<tt>#{val[1]}</tt>", val[1]

    result
end

# reduce 103 omitted

# reduce 104 omitted

# reduce 105 omitted

# reduce 106 omitted

# reduce 107 omitted

# reduce 108 omitted

def _reduce_109(val, _values, result)
 result << val[1]
    result
end

# reduce 110 omitted

def _reduce_111(val, _values, result)
      result = inline val[0]

    result
end

# reduce 112 omitted

def _reduce_113(val, _values, result)
 result = val[1]
    result
end

def _reduce_114(val, _values, result)
 result = val[1]
    result
end

def _reduce_115(val, _values, result)
 result = val[1]
    result
end

# reduce 116 omitted

# reduce 117 omitted

# reduce 118 omitted

# reduce 119 omitted

# reduce 120 omitted

# reduce 121 omitted

# reduce 122 omitted

# reduce 123 omitted

# reduce 124 omitted

# reduce 125 omitted

# reduce 126 omitted

# reduce 127 omitted

# reduce 128 omitted

# reduce 129 omitted

# reduce 130 omitted

# reduce 131 omitted

# reduce 132 omitted

# reduce 133 omitted

# reduce 134 omitted

# reduce 135 omitted

def _reduce_136(val, _values, result)
 result << val[1]
    result
end

# reduce 137 omitted

def _reduce_none(val, _values, result)
  val[0]
end

end   # class InlineParser

end

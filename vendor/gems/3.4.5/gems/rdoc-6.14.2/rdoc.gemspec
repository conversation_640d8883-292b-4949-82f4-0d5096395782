begin
  require_relative "lib/rdoc/version"
rescue LoadError
  # for Ruby repository
  require_relative "version"
end

Gem::Specification.new do |s|
  s.name = "rdoc"
  s.version = RDoc::VERSION

  s.authors = [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON><PERSON><PERSON>",
    "ITOYANAGI Sakura"
  ]
  s.email = ["<EMAIL>", "", "", "", "<EMAIL>", "<EMAIL>", "<EMAIL>"]

  s.summary = "RDoc produces HTML and command-line documentation for Ruby projects"
  s.description = <<-DESCRIPTION
RDoc produces HTML and command-line documentation for Ruby projects.
RDoc includes the +rdoc+ and +ri+ tools for generating and displaying documentation from the command-line.
  DESCRIPTION
  s.homepage = "https://ruby.github.io/rdoc"
  s.licenses = ["Ruby"]

  s.metadata["homepage_uri"] = s.homepage
  s.metadata["source_code_uri"] = "https://github.com/ruby/rdoc"
  s.metadata["changelog_uri"] = "#{s.metadata["source_code_uri"]}/releases"

  s.bindir = "exe"
  s.executables = ["rdoc", "ri"]
  s.require_paths = ["lib"]
  # for ruby core repository. It was generated by
  # `git ls-files -z`.split("\x0").each {|f| puts "    #{f.dump}," unless f.start_with?(*%W[test/ spec/ features/ .]) }
  non_lib_files = [
    "CONTRIBUTING.rdoc",
    "CVE-2013-0256.rdoc",
    "ExampleMarkdown.md",
    "ExampleRDoc.rdoc",
    "History.rdoc",
    "LEGAL.rdoc",
    "LICENSE.rdoc",
    "README.rdoc",
    "RI.md",
    "TODO.rdoc",
    "exe/rdoc",
    "exe/ri",
    "man/ri.1",
    "rdoc.gemspec",
  ]
  base = __dir__
  not_dir = ->(path) {!File.directory?(File.join(base, path))}
  template_files = Dir.glob("lib/rdoc/generator/template/**/*", base: base).select(&not_dir)
  lib_files = Dir.glob("lib/**/*.{rb,kpeg,ry}", base: base).select(&not_dir)

  s.files = (non_lib_files + template_files + lib_files).uniq

  s.rdoc_options = ["--main", "README.rdoc"]
  s.extra_rdoc_files += s.files.grep(%r[\A[^\/]+\.(?:rdoc|md)\z])

  s.required_ruby_version = Gem::Requirement.new(">= 2.6.0")
  s.required_rubygems_version = Gem::Requirement.new(">= 2.2")

  s.add_dependency 'psych', '>= 4.0.0'
  s.add_dependency 'erb'
end

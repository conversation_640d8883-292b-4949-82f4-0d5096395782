= TODO
This file contains some things that might happen in RDoc, or might not.
Forward Looking Statements applies.

== RDoc::VERSION.succ

=== Blockers:

* Update LICENSE to match ruby's switch
* The alias keyword should not be bidirectional
* Fix RDoc::Parser#use_markup to handle the filename (see TODO)
* Restore backwards compatibility due to paragraph text joining from existing
  ri files
* Fix consumption of , after link like: RDoc[rdoc-ref:RDoc], <- comma here
* Remove support for links like Matrix[*rows]

=== Nice to have:

* Parse only changed files (like in ruby)
* Page of Glory (or Shame) in HTML output showing documentation coverage
  statistics.
* Link to the parent-class implementation of methods that use super
* Add direct accessor to RDoc::Options to RDoc::Task
* Remove "Public" in HTML output if there are only public methods
* Method markup support for rd documentation (per rd syntax)
* Improve SIGINFO handling
* Global variable support
* Provide the code_object to directive handlers

== More Future

=== API changes to RDoc

* RDoc::TopLevel#add_method should automatically create the appropriate method
  class rather than requiring one be passed in.
* Remove #comment= from Context subclasses in favor of #add_comment
* Add versions to RDoc::Markup syntax tree marshal format
* Comments can no longer be Strings

== Crazy Ideas

* Auto-normalize heading levels to look OK.  It's weird to see an <h1> in
  the middle of a method section.
* RDoc::CodeObject
  * Move into own namespace
  * Rename TopLevel to File
  * Rename Context to Container
  * Rename NormalClass to Class

== Accessibility

Page title in right hand side

Table of contents in left hand side as sub-list under main heading

For class list, never method list, method summary at top

table-of-contents-navigation div => nav + role="navigation"

type "mod", focus is still on "mod"

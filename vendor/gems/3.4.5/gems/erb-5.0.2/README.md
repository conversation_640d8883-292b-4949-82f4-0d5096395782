# ERB

An easy to use but powerful templating system for Ruby.

## Introduction

ERB provides an easy to use but powerful templating system for Ruby.  Using
ERB, actual Ruby code can be added to any plain text document for the
purposes of generating document information details and/or flow control.

A very simple example is this:

```rb
require 'erb'

x = 42
template = ERB.new <<-EOF
  The value of x is: <%= x %>
EO<PERSON>
puts template.result(binding)
```

Prints: `The value of x is: 42`

More complex examples are given below.

## Recognized Tags

ERB recognizes certain tags in the provided template and converts them based
on the rules below:

```erb
<% Ruby code -- inline with output %>
<%= Ruby expression -- replace with result %>
<%# comment -- ignored -- useful in testing %> (`<% #` doesn't work. Don't use Ruby comments.)
% a line of Ruby code -- treated as <% line %> (optional -- see ERB.new)
%% replaced with % if first thing on a line and % processing is used
<%% or %%> -- replace with <% or %> respectively
```

All other text is passed through ERB filtering unchanged.

## Options

There are several settings you can change when you use ERB:
* the nature of the tags that are recognized;
* the binding used to resolve local variables in the template.

See the ERB.new and ERB#result methods for more detail.

## Character encodings

ERB (or Ruby code generated by ERB) returns a string in the same
character encoding as the input string.  When the input string has
a magic comment, however, it returns a string in the encoding specified
by the magic comment.

```rb
# -*- coding: utf-8 -*-
require 'erb'

template = ERB.new <<EOF
<%#-*- coding: Big5 -*-%>
  __ENCODING__ is <%= __ENCODING__ %>.
EOF
puts template.result
```

Prints: `__ENCODING__ is Big5.`

## Examples

### Plain Text

ERB is useful for any generic templating situation.  Note that in this example, we use the
convenient "% at start of line" tag, and we quote the template literally with
`%q{...}` to avoid trouble with the backslash.

```rb
require "erb"

# Create template.
template = %q{
  From:  James Edward Gray II <<EMAIL>>
  To:  <%= to %>
  Subject:  Addressing Needs

  <%= to[/\w+/] %>:

  Just wanted to send a quick note assuring that your needs are being
  addressed.

  I want you to know that my team will keep working on the issues,
  especially:

  <%# ignore numerous minor requests -- focus on priorities %>
  % priorities.each do |priority|
    * <%= priority %>
  % end

  Thanks for your patience.

  James Edward Gray II
}.gsub(/^  /, '')

message = ERB.new(template, trim_mode: "%<>")

# Set up template data.
to = "Community Spokesman <spokesman@ruby_community.org>"
priorities = [ "Run Ruby Quiz",
               "Document Modules",
               "Answer Questions on Ruby Talk" ]

# Produce result.
email = message.result
puts email
```

Generates:

```
From:  James Edward Gray II <<EMAIL>>
To:  Community Spokesman <spokesman@ruby_community.org>
Subject:  Addressing Needs

Community:

Just wanted to send a quick note assuring that your needs are being addressed.

I want you to know that my team will keep working on the issues, especially:

    * Run Ruby Quiz
    * Document Modules
    * Answer Questions on Ruby Talk

Thanks for your patience.

James Edward Gray II
```

### Ruby in HTML

ERB is often used in .rhtml files (HTML with embedded Ruby).  Notice the need in
this example to provide a special binding when the template is run, so that the instance
variables in the Product object can be resolved.

```rb
require "erb"

# Build template data class.
class Product
  def initialize( code, name, desc, cost )
    @code = code
    @name = name
    @desc = desc
    @cost = cost

    @features = [ ]
  end

  def add_feature( feature )
    @features << feature
  end

  # Support templating of member data.
  def get_binding
    binding
  end

  # ...
end

# Create template.
template = %{
  <html>
    <head><title>Ruby Toys -- <%= @name %></title></head>
    <body>

      <h1><%= @name %> (<%= @code %>)</h1>
      <p><%= @desc %></p>

      <ul>
        <% @features.each do |f| %>
          <li><b><%= f %></b></li>
        <% end %>
      </ul>

      <p>
        <% if @cost < 10 %>
          <b>Only <%= @cost %>!!!</b>
        <% else %>
           Call for a price, today!
        <% end %>
      </p>

    </body>
  </html>
}.gsub(/^  /, '')

rhtml = ERB.new(template)

# Set up template data.
toy = Product.new( "TZ-1002",
                   "Rubysapien",
                   "Geek's Best Friend!  Responds to Ruby commands...",
                   999.95 )
toy.add_feature("Listens for verbal commands in the Ruby language!")
toy.add_feature("Ignores Perl, Java, and all C variants.")
toy.add_feature("Karate-Chop Action!!!")
toy.add_feature("Matz signature on left leg.")
toy.add_feature("Gem studded eyes... Rubies, of course!")

# Produce result.
rhtml.run(toy.get_binding)
```

Generates (some blank lines removed):

```html
<html>
  <head><title>Ruby Toys -- Rubysapien</title></head>
  <body>

    <h1>Rubysapien (TZ-1002)</h1>
    <p>Geek's Best Friend!  Responds to Ruby commands...</p>

    <ul>
        <li><b>Listens for verbal commands in the Ruby language!</b></li>
        <li><b>Ignores Perl, Java, and all C variants.</b></li>
        <li><b>Karate-Chop Action!!!</b></li>
        <li><b>Matz signature on left leg.</b></li>
        <li><b>Gem studded eyes... Rubies, of course!</b></li>
    </ul>

    <p>
         Call for a price, today!
    </p>

  </body>
</html>
```

## Notes

There are a variety of templating solutions available in various Ruby projects.
For example, RDoc, distributed with Ruby, uses its own template engine, which
can be reused elsewhere.

Other popular engines could be found in the corresponding
[Category](https://www.ruby-toolbox.com/categories/template_engines) of
The Ruby Toolbox.

## License

The gem is available as open source under the terms of the [2-Clause BSD License](https://opensource.org/licenses/BSD-2-Clause).

name: test

on:
  push:
    branches: [master]
  pull_request:
  workflow_dispatch:

jobs:
  ruby-versions:
    uses: ruby/actions/.github/workflows/ruby_versions.yml@master
    with:
      engine: cruby
      versions: '["jruby", "truffleruby-head"]'

  test:
    needs: ruby-versions
    name: build (${{ matrix.ruby }} / ${{ matrix.os }})
    strategy:
      matrix:
        ruby: ${{ fromJson(needs.ruby-versions.outputs.versions) }}
        os: [ubuntu-latest]
      fail-fast: false
    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v4
    - name: Set up Ruby
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: ${{ matrix.ruby }}
        bundler-cache: true
    - name: Run test
      run: bundle exec rake test

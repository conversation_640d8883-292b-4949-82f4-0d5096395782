# frozen_string_literal: true

require "bundler/gem_tasks"
<% default_task_names = [config[:test_task]].compact -%>
<% case config[:test] -%>
<% when "minitest" -%>
require "minitest/test_task"

Minitest::TestTask.create

<% when "test-unit" -%>
require "rake/testtask"

Rake::TestTask.new(:test) do |t|
  t.libs << "test"
  t.libs << "lib"
  t.test_files = FileList["test/**/*_test.rb"]
end

<% when "rspec" -%>
require "rspec/core/rake_task"

RSpec::Core::RakeTask.new(:spec)

<% end -%>
<% if config[:linter] == "rubocop" -%>
<% default_task_names << :rubocop -%>
require "rubocop/rake_task"

RuboCop::RakeTask.new

<% elsif config[:linter] == "standard" -%>
<% default_task_names << :standard -%>
require "standard/rake"

<% end -%>
<% if config[:ext] -%>
<% default_task_names.unshift(:compile) -%>
<% default_task_names.unshift(:clobber) unless config[:ext] == 'rust' -%>
<% if config[:ext] == 'rust' -%>
require "rb_sys/extensiontask"

task build: :compile

GEMSPEC = Gem::Specification.load("<%= config[:underscored_name] %>.gemspec")

RbSys::ExtensionTask.new(<%= config[:name].inspect %>, GEMSPEC) do |ext|
  ext.lib_dir = "lib/<%= config[:namespaced_path] %>"
end
<% else -%>
require "rake/extensiontask"

task build: :compile

GEMSPEC = Gem::Specification.load("<%= config[:underscored_name] %>.gemspec")

Rake::ExtensionTask.new("<%= config[:underscored_name] %>", GEMSPEC) do |ext|
  ext.lib_dir = "lib/<%= config[:namespaced_path] %>"
end
<% end -%>

<% end -%>
<% if default_task_names.size == 1 -%>
task default: <%= default_task_names.first.inspect %>
<% else -%>
task default: %i[<%= default_task_names.join(" ") %>]
<% end -%>

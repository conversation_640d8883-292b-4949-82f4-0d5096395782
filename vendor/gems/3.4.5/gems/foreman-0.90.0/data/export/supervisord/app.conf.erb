<%
app_names = []
engine.each_process do |name, process|
  1.upto(engine.formation[name]) do |num|
    port = engine.port_for(process, num)
    full_name = "#{app}-#{name}-#{num}"
    environment = engine.env.merge("PORT" => port.to_s).map do |key, value|
    	value = shell_quote(value)
    	value = value.gsub('\=', '=')
    	value = value.gsub('\&', '&')
    	value = value.gsub('\?', '?')
      "#{key}=\"#{value}\""
    end
    app_names << full_name
-%>
[program:<%= full_name %>]
command=<%= process.command %>
autostart=true
autorestart=true
stdout_logfile=<%= log %>/<%= name %>-<%= num %>.log
stderr_logfile=<%= log %>/<%= name %>-<%= num %>.error.log
user=<%= user %>
directory=<%= engine.root %>
environment=<%= environment.join(',') %>

<%
  end
end
-%>
[group:<%= app %>]
programs=<%= app_names.join(',') %>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string><%= "#{app}-#{name}-#{num}" %></string>
    <key>EnvironmentVariables</key>
    <dict>
    <%- engine.env.merge("PORT" => port).each_pair do |var,env| -%>
        <key><%= var %></key>
        <string><%= env %></string>
    <%- end -%>
    </dict>
    <key>ProgramArguments</key>
    <array>
    <%- command_args.each do |command| -%>
        <string><%= command %></string>
    <%- end -%>
    </array>
    <key>KeepAlive</key>
    <true/>
    <key>RunAtLoad</key>
    <true/>
    <key>StandardOutPath</key>
    <string><%= log %>/<%= app %>-<%= name %>-<%=num%>.log</string>
    <key>StandardErrorPath</key>
    <string><%= log %>/<%= app %>-<%= name %>-<%=num%>.log</string>
    <key>UserName</key>
    <string><%= user %></string>
    <key>WorkingDirectory</key>
    <string><%= engine.root %></string>
</dict>
</plist>

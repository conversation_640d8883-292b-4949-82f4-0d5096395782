name: Publish gem to rubygems.org

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: read

jobs:
  push:
    if: github.repository == 'ruby/benchmark'
    runs-on: ubuntu-latest

    environment:
      name: rubygems.org
      url: https://rubygems.org/gems/benchmark

    permissions:
      contents: write
      id-token: write

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - uses: actions/checkout@0ad4b8fadaa221de15dcec353f45205ec38ea70b # v4.1.4

      - name: Set up Ruby
        uses: ruby/setup-ruby@a6e6f86333f0a2523ece813039b8b4be04560854 # v1.190.0
        with:
          bundler-cache: true
          ruby-version: ruby

      - name: Publish to RubyGems
        uses: rubygems/release-gem@a25424ba2ba8b387abc8ef40807c2c85b96cbe32 # v1.1.1

      - name: Create GitHub release
        run: |
          tag_name="$(git describe --tags --abbrev=0)"
          gh release create "${tag_name}" --verify-tag --generate-notes
        env:
          GITHUB_TOKEN: ${{ secrets.MATZBOT_GITHUB_WORKFLOW_TOKEN }}

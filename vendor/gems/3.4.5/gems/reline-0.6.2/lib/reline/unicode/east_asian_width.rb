class Reline::Unicode::EastAsianWidth
  # This is based on EastAsianWidth.txt
  # UNICODE_VERSION = '16.0.0'

  CHUNK_LAST, CHUNK_WIDTH = [
    [0x1f, 2],
    [0x7e, 1],
    [0x7f, 2],
    [0xa0, 1],
    [0xa1, -1],
    [0xa3, 1],
    [0xa4, -1],
    [0xa6, 1],
    [0xa8, -1],
    [0xa9, 1],
    [0xaa, -1],
    [0xac, 1],
    [0xae, -1],
    [0xaf, 1],
    [0xb4, -1],
    [0xb5, 1],
    [0xba, -1],
    [0xbb, 1],
    [0xbf, -1],
    [0xc5, 1],
    [0xc6, -1],
    [0xcf, 1],
    [0xd0, -1],
    [0xd6, 1],
    [0xd8, -1],
    [0xdd, 1],
    [0xe1, -1],
    [0xe5, 1],
    [0xe6, -1],
    [0xe7, 1],
    [0xea, -1],
    [0xeb, 1],
    [0xed, -1],
    [0xef, 1],
    [0xf0, -1],
    [0xf1, 1],
    [0xf3, -1],
    [0xf6, 1],
    [0xfa, -1],
    [0xfb, 1],
    [0xfc, -1],
    [0xfd, 1],
    [0xfe, -1],
    [0x100, 1],
    [0x101, -1],
    [0x110, 1],
    [0x111, -1],
    [0x112, 1],
    [0x113, -1],
    [0x11a, 1],
    [0x11b, -1],
    [0x125, 1],
    [0x127, -1],
    [0x12a, 1],
    [0x12b, -1],
    [0x130, 1],
    [0x133, -1],
    [0x137, 1],
    [0x138, -1],
    [0x13e, 1],
    [0x142, -1],
    [0x143, 1],
    [0x144, -1],
    [0x147, 1],
    [0x14b, -1],
    [0x14c, 1],
    [0x14d, -1],
    [0x151, 1],
    [0x153, -1],
    [0x165, 1],
    [0x167, -1],
    [0x16a, 1],
    [0x16b, -1],
    [0x1cd, 1],
    [0x1ce, -1],
    [0x1cf, 1],
    [0x1d0, -1],
    [0x1d1, 1],
    [0x1d2, -1],
    [0x1d3, 1],
    [0x1d4, -1],
    [0x1d5, 1],
    [0x1d6, -1],
    [0x1d7, 1],
    [0x1d8, -1],
    [0x1d9, 1],
    [0x1da, -1],
    [0x1db, 1],
    [0x1dc, -1],
    [0x250, 1],
    [0x251, -1],
    [0x260, 1],
    [0x261, -1],
    [0x2c3, 1],
    [0x2c4, -1],
    [0x2c6, 1],
    [0x2c7, -1],
    [0x2c8, 1],
    [0x2cb, -1],
    [0x2cc, 1],
    [0x2cd, -1],
    [0x2cf, 1],
    [0x2d0, -1],
    [0x2d7, 1],
    [0x2db, -1],
    [0x2dc, 1],
    [0x2dd, -1],
    [0x2de, 1],
    [0x2df, -1],
    [0x2ff, 1],
    [0x36f, 0],
    [0x390, 1],
    [0x3a1, -1],
    [0x3a2, 1],
    [0x3a9, -1],
    [0x3b0, 1],
    [0x3c1, -1],
    [0x3c2, 1],
    [0x3c9, -1],
    [0x400, 1],
    [0x401, -1],
    [0x40f, 1],
    [0x44f, -1],
    [0x450, 1],
    [0x451, -1],
    [0x482, 1],
    [0x489, 0],
    [0x590, 1],
    [0x5bd, 0],
    [0x5be, 1],
    [0x5bf, 0],
    [0x5c0, 1],
    [0x5c2, 0],
    [0x5c3, 1],
    [0x5c5, 0],
    [0x5c6, 1],
    [0x5c7, 0],
    [0x60f, 1],
    [0x61a, 0],
    [0x64a, 1],
    [0x65f, 0],
    [0x66f, 1],
    [0x670, 0],
    [0x6d5, 1],
    [0x6dc, 0],
    [0x6de, 1],
    [0x6e4, 0],
    [0x6e6, 1],
    [0x6e8, 0],
    [0x6e9, 1],
    [0x6ed, 0],
    [0x710, 1],
    [0x711, 0],
    [0x72f, 1],
    [0x74a, 0],
    [0x7a5, 1],
    [0x7b0, 0],
    [0x7ea, 1],
    [0x7f3, 0],
    [0x7fc, 1],
    [0x7fd, 0],
    [0x815, 1],
    [0x819, 0],
    [0x81a, 1],
    [0x823, 0],
    [0x824, 1],
    [0x827, 0],
    [0x828, 1],
    [0x82d, 0],
    [0x858, 1],
    [0x85b, 0],
    [0x896, 1],
    [0x89f, 0],
    [0x8c9, 1],
    [0x8e1, 0],
    [0x8e2, 1],
    [0x902, 0],
    [0x939, 1],
    [0x93a, 0],
    [0x93b, 1],
    [0x93c, 0],
    [0x940, 1],
    [0x948, 0],
    [0x94c, 1],
    [0x94d, 0],
    [0x950, 1],
    [0x957, 0],
    [0x961, 1],
    [0x963, 0],
    [0x980, 1],
    [0x981, 0],
    [0x9bb, 1],
    [0x9bc, 0],
    [0x9c0, 1],
    [0x9c4, 0],
    [0x9cc, 1],
    [0x9cd, 0],
    [0x9e1, 1],
    [0x9e3, 0],
    [0x9fd, 1],
    [0x9fe, 0],
    [0xa00, 1],
    [0xa02, 0],
    [0xa3b, 1],
    [0xa3c, 0],
    [0xa40, 1],
    [0xa42, 0],
    [0xa46, 1],
    [0xa48, 0],
    [0xa4a, 1],
    [0xa4d, 0],
    [0xa50, 1],
    [0xa51, 0],
    [0xa6f, 1],
    [0xa71, 0],
    [0xa74, 1],
    [0xa75, 0],
    [0xa80, 1],
    [0xa82, 0],
    [0xabb, 1],
    [0xabc, 0],
    [0xac0, 1],
    [0xac5, 0],
    [0xac6, 1],
    [0xac8, 0],
    [0xacc, 1],
    [0xacd, 0],
    [0xae1, 1],
    [0xae3, 0],
    [0xaf9, 1],
    [0xaff, 0],
    [0xb00, 1],
    [0xb01, 0],
    [0xb3b, 1],
    [0xb3c, 0],
    [0xb3e, 1],
    [0xb3f, 0],
    [0xb40, 1],
    [0xb44, 0],
    [0xb4c, 1],
    [0xb4d, 0],
    [0xb54, 1],
    [0xb56, 0],
    [0xb61, 1],
    [0xb63, 0],
    [0xb81, 1],
    [0xb82, 0],
    [0xbbf, 1],
    [0xbc0, 0],
    [0xbcc, 1],
    [0xbcd, 0],
    [0xbff, 1],
    [0xc00, 0],
    [0xc03, 1],
    [0xc04, 0],
    [0xc3b, 1],
    [0xc3c, 0],
    [0xc3d, 1],
    [0xc40, 0],
    [0xc45, 1],
    [0xc48, 0],
    [0xc49, 1],
    [0xc4d, 0],
    [0xc54, 1],
    [0xc56, 0],
    [0xc61, 1],
    [0xc63, 0],
    [0xc80, 1],
    [0xc81, 0],
    [0xcbb, 1],
    [0xcbc, 0],
    [0xcbe, 1],
    [0xcbf, 0],
    [0xcc5, 1],
    [0xcc6, 0],
    [0xccb, 1],
    [0xccd, 0],
    [0xce1, 1],
    [0xce3, 0],
    [0xcff, 1],
    [0xd01, 0],
    [0xd3a, 1],
    [0xd3c, 0],
    [0xd40, 1],
    [0xd44, 0],
    [0xd4c, 1],
    [0xd4d, 0],
    [0xd61, 1],
    [0xd63, 0],
    [0xd80, 1],
    [0xd81, 0],
    [0xdc9, 1],
    [0xdca, 0],
    [0xdd1, 1],
    [0xdd4, 0],
    [0xdd5, 1],
    [0xdd6, 0],
    [0xe30, 1],
    [0xe31, 0],
    [0xe33, 1],
    [0xe3a, 0],
    [0xe46, 1],
    [0xe4e, 0],
    [0xeb0, 1],
    [0xeb1, 0],
    [0xeb3, 1],
    [0xebc, 0],
    [0xec7, 1],
    [0xece, 0],
    [0xf17, 1],
    [0xf19, 0],
    [0xf34, 1],
    [0xf35, 0],
    [0xf36, 1],
    [0xf37, 0],
    [0xf38, 1],
    [0xf39, 0],
    [0xf70, 1],
    [0xf7e, 0],
    [0xf7f, 1],
    [0xf84, 0],
    [0xf85, 1],
    [0xf87, 0],
    [0xf8c, 1],
    [0xf97, 0],
    [0xf98, 1],
    [0xfbc, 0],
    [0xfc5, 1],
    [0xfc6, 0],
    [0x102c, 1],
    [0x1030, 0],
    [0x1031, 1],
    [0x1037, 0],
    [0x1038, 1],
    [0x103a, 0],
    [0x103c, 1],
    [0x103e, 0],
    [0x1057, 1],
    [0x1059, 0],
    [0x105d, 1],
    [0x1060, 0],
    [0x1070, 1],
    [0x1074, 0],
    [0x1081, 1],
    [0x1082, 0],
    [0x1084, 1],
    [0x1086, 0],
    [0x108c, 1],
    [0x108d, 0],
    [0x109c, 1],
    [0x109d, 0],
    [0x10ff, 1],
    [0x115f, 2],
    [0x11ff, 0],
    [0x135c, 1],
    [0x135f, 0],
    [0x1711, 1],
    [0x1714, 0],
    [0x1731, 1],
    [0x1733, 0],
    [0x1751, 1],
    [0x1753, 0],
    [0x1771, 1],
    [0x1773, 0],
    [0x17b3, 1],
    [0x17b5, 0],
    [0x17b6, 1],
    [0x17bd, 0],
    [0x17c5, 1],
    [0x17c6, 0],
    [0x17c8, 1],
    [0x17d3, 0],
    [0x17dc, 1],
    [0x17dd, 0],
    [0x180a, 1],
    [0x180d, 0],
    [0x180e, 1],
    [0x180f, 0],
    [0x1884, 1],
    [0x1886, 0],
    [0x18a8, 1],
    [0x18a9, 0],
    [0x191f, 1],
    [0x1922, 0],
    [0x1926, 1],
    [0x1928, 0],
    [0x1931, 1],
    [0x1932, 0],
    [0x1938, 1],
    [0x193b, 0],
    [0x1a16, 1],
    [0x1a18, 0],
    [0x1a1a, 1],
    [0x1a1b, 0],
    [0x1a55, 1],
    [0x1a56, 0],
    [0x1a57, 1],
    [0x1a5e, 0],
    [0x1a5f, 1],
    [0x1a60, 0],
    [0x1a61, 1],
    [0x1a62, 0],
    [0x1a64, 1],
    [0x1a6c, 0],
    [0x1a72, 1],
    [0x1a7c, 0],
    [0x1a7e, 1],
    [0x1a7f, 0],
    [0x1aaf, 1],
    [0x1ace, 0],
    [0x1aff, 1],
    [0x1b03, 0],
    [0x1b33, 1],
    [0x1b34, 0],
    [0x1b35, 1],
    [0x1b3a, 0],
    [0x1b3b, 1],
    [0x1b3c, 0],
    [0x1b41, 1],
    [0x1b42, 0],
    [0x1b6a, 1],
    [0x1b73, 0],
    [0x1b7f, 1],
    [0x1b81, 0],
    [0x1ba1, 1],
    [0x1ba5, 0],
    [0x1ba7, 1],
    [0x1ba9, 0],
    [0x1baa, 1],
    [0x1bad, 0],
    [0x1be5, 1],
    [0x1be6, 0],
    [0x1be7, 1],
    [0x1be9, 0],
    [0x1bec, 1],
    [0x1bed, 0],
    [0x1bee, 1],
    [0x1bf1, 0],
    [0x1c2b, 1],
    [0x1c33, 0],
    [0x1c35, 1],
    [0x1c37, 0],
    [0x1ccf, 1],
    [0x1cd2, 0],
    [0x1cd3, 1],
    [0x1ce0, 0],
    [0x1ce1, 1],
    [0x1ce8, 0],
    [0x1cec, 1],
    [0x1ced, 0],
    [0x1cf3, 1],
    [0x1cf4, 0],
    [0x1cf7, 1],
    [0x1cf9, 0],
    [0x1dbf, 1],
    [0x1dff, 0],
    [0x200f, 1],
    [0x2010, -1],
    [0x2012, 1],
    [0x2016, -1],
    [0x2017, 1],
    [0x2019, -1],
    [0x201b, 1],
    [0x201d, -1],
    [0x201f, 1],
    [0x2022, -1],
    [0x2023, 1],
    [0x2027, -1],
    [0x202f, 1],
    [0x2030, -1],
    [0x2031, 1],
    [0x2033, -1],
    [0x2034, 1],
    [0x2035, -1],
    [0x203a, 1],
    [0x203b, -1],
    [0x203d, 1],
    [0x203e, -1],
    [0x2073, 1],
    [0x2074, -1],
    [0x207e, 1],
    [0x207f, -1],
    [0x2080, 1],
    [0x2084, -1],
    [0x20ab, 1],
    [0x20ac, -1],
    [0x20cf, 1],
    [0x20f0, 0],
    [0x2102, 1],
    [0x2103, -1],
    [0x2104, 1],
    [0x2105, -1],
    [0x2108, 1],
    [0x2109, -1],
    [0x2112, 1],
    [0x2113, -1],
    [0x2115, 1],
    [0x2116, -1],
    [0x2120, 1],
    [0x2122, -1],
    [0x2125, 1],
    [0x2126, -1],
    [0x212a, 1],
    [0x212b, -1],
    [0x2152, 1],
    [0x2154, -1],
    [0x215a, 1],
    [0x215e, -1],
    [0x215f, 1],
    [0x216b, -1],
    [0x216f, 1],
    [0x2179, -1],
    [0x2188, 1],
    [0x2189, -1],
    [0x218f, 1],
    [0x2199, -1],
    [0x21b7, 1],
    [0x21b9, -1],
    [0x21d1, 1],
    [0x21d2, -1],
    [0x21d3, 1],
    [0x21d4, -1],
    [0x21e6, 1],
    [0x21e7, -1],
    [0x21ff, 1],
    [0x2200, -1],
    [0x2201, 1],
    [0x2203, -1],
    [0x2206, 1],
    [0x2208, -1],
    [0x220a, 1],
    [0x220b, -1],
    [0x220e, 1],
    [0x220f, -1],
    [0x2210, 1],
    [0x2211, -1],
    [0x2214, 1],
    [0x2215, -1],
    [0x2219, 1],
    [0x221a, -1],
    [0x221c, 1],
    [0x2220, -1],
    [0x2222, 1],
    [0x2223, -1],
    [0x2224, 1],
    [0x2225, -1],
    [0x2226, 1],
    [0x222c, -1],
    [0x222d, 1],
    [0x222e, -1],
    [0x2233, 1],
    [0x2237, -1],
    [0x223b, 1],
    [0x223d, -1],
    [0x2247, 1],
    [0x2248, -1],
    [0x224b, 1],
    [0x224c, -1],
    [0x2251, 1],
    [0x2252, -1],
    [0x225f, 1],
    [0x2261, -1],
    [0x2263, 1],
    [0x2267, -1],
    [0x2269, 1],
    [0x226b, -1],
    [0x226d, 1],
    [0x226f, -1],
    [0x2281, 1],
    [0x2283, -1],
    [0x2285, 1],
    [0x2287, -1],
    [0x2294, 1],
    [0x2295, -1],
    [0x2298, 1],
    [0x2299, -1],
    [0x22a4, 1],
    [0x22a5, -1],
    [0x22be, 1],
    [0x22bf, -1],
    [0x2311, 1],
    [0x2312, -1],
    [0x2319, 1],
    [0x231b, 2],
    [0x2328, 1],
    [0x232a, 2],
    [0x23e8, 1],
    [0x23ec, 2],
    [0x23ef, 1],
    [0x23f0, 2],
    [0x23f2, 1],
    [0x23f3, 2],
    [0x245f, 1],
    [0x24e9, -1],
    [0x24ea, 1],
    [0x254b, -1],
    [0x254f, 1],
    [0x2573, -1],
    [0x257f, 1],
    [0x258f, -1],
    [0x2591, 1],
    [0x2595, -1],
    [0x259f, 1],
    [0x25a1, -1],
    [0x25a2, 1],
    [0x25a9, -1],
    [0x25b1, 1],
    [0x25b3, -1],
    [0x25b5, 1],
    [0x25b7, -1],
    [0x25bb, 1],
    [0x25bd, -1],
    [0x25bf, 1],
    [0x25c1, -1],
    [0x25c5, 1],
    [0x25c8, -1],
    [0x25ca, 1],
    [0x25cb, -1],
    [0x25cd, 1],
    [0x25d1, -1],
    [0x25e1, 1],
    [0x25e5, -1],
    [0x25ee, 1],
    [0x25ef, -1],
    [0x25fc, 1],
    [0x25fe, 2],
    [0x2604, 1],
    [0x2606, -1],
    [0x2608, 1],
    [0x2609, -1],
    [0x260d, 1],
    [0x260f, -1],
    [0x2613, 1],
    [0x2615, 2],
    [0x261b, 1],
    [0x261c, -1],
    [0x261d, 1],
    [0x261e, -1],
    [0x262f, 1],
    [0x2637, 2],
    [0x263f, 1],
    [0x2640, -1],
    [0x2641, 1],
    [0x2642, -1],
    [0x2647, 1],
    [0x2653, 2],
    [0x265f, 1],
    [0x2661, -1],
    [0x2662, 1],
    [0x2665, -1],
    [0x2666, 1],
    [0x266a, -1],
    [0x266b, 1],
    [0x266d, -1],
    [0x266e, 1],
    [0x266f, -1],
    [0x267e, 1],
    [0x267f, 2],
    [0x2689, 1],
    [0x268f, 2],
    [0x2692, 1],
    [0x2693, 2],
    [0x269d, 1],
    [0x269f, -1],
    [0x26a0, 1],
    [0x26a1, 2],
    [0x26a9, 1],
    [0x26ab, 2],
    [0x26bc, 1],
    [0x26be, 2],
    [0x26bf, -1],
    [0x26c3, 1],
    [0x26c5, 2],
    [0x26cd, -1],
    [0x26ce, 2],
    [0x26d3, -1],
    [0x26d4, 2],
    [0x26e1, -1],
    [0x26e2, 1],
    [0x26e3, -1],
    [0x26e7, 1],
    [0x26e9, -1],
    [0x26ea, 2],
    [0x26f1, -1],
    [0x26f3, 2],
    [0x26f4, -1],
    [0x26f5, 2],
    [0x26f9, -1],
    [0x26fa, 2],
    [0x26fc, -1],
    [0x26fd, 2],
    [0x26ff, -1],
    [0x2704, 1],
    [0x2705, 2],
    [0x2709, 1],
    [0x270b, 2],
    [0x2727, 1],
    [0x2728, 2],
    [0x273c, 1],
    [0x273d, -1],
    [0x274b, 1],
    [0x274c, 2],
    [0x274d, 1],
    [0x274e, 2],
    [0x2752, 1],
    [0x2755, 2],
    [0x2756, 1],
    [0x2757, 2],
    [0x2775, 1],
    [0x277f, -1],
    [0x2794, 1],
    [0x2797, 2],
    [0x27af, 1],
    [0x27b0, 2],
    [0x27be, 1],
    [0x27bf, 2],
    [0x2b1a, 1],
    [0x2b1c, 2],
    [0x2b4f, 1],
    [0x2b50, 2],
    [0x2b54, 1],
    [0x2b55, 2],
    [0x2b59, -1],
    [0x2cee, 1],
    [0x2cf1, 0],
    [0x2d7e, 1],
    [0x2d7f, 0],
    [0x2ddf, 1],
    [0x2dff, 0],
    [0x2e7f, 1],
    [0x2e99, 2],
    [0x2e9a, 1],
    [0x2ef3, 2],
    [0x2eff, 1],
    [0x2fd5, 2],
    [0x2fef, 1],
    [0x3029, 2],
    [0x302d, 0],
    [0x303e, 2],
    [0x3040, 1],
    [0x3096, 2],
    [0x3098, 1],
    [0x309a, 0],
    [0x30ff, 2],
    [0x3104, 1],
    [0x312f, 2],
    [0x3130, 1],
    [0x318e, 2],
    [0x318f, 1],
    [0x31e5, 2],
    [0x31ee, 1],
    [0x321e, 2],
    [0x321f, 1],
    [0x3247, 2],
    [0x324f, -1],
    [0xa48c, 2],
    [0xa48f, 1],
    [0xa4c6, 2],
    [0xa66e, 1],
    [0xa672, 0],
    [0xa673, 1],
    [0xa67d, 0],
    [0xa69d, 1],
    [0xa69f, 0],
    [0xa6ef, 1],
    [0xa6f1, 0],
    [0xa801, 1],
    [0xa802, 0],
    [0xa805, 1],
    [0xa806, 0],
    [0xa80a, 1],
    [0xa80b, 0],
    [0xa824, 1],
    [0xa826, 0],
    [0xa82b, 1],
    [0xa82c, 0],
    [0xa8c3, 1],
    [0xa8c5, 0],
    [0xa8df, 1],
    [0xa8f1, 0],
    [0xa8fe, 1],
    [0xa8ff, 0],
    [0xa925, 1],
    [0xa92d, 0],
    [0xa946, 1],
    [0xa951, 0],
    [0xa95f, 1],
    [0xa97c, 2],
    [0xa97f, 1],
    [0xa982, 0],
    [0xa9b2, 1],
    [0xa9b3, 0],
    [0xa9b5, 1],
    [0xa9b9, 0],
    [0xa9bb, 1],
    [0xa9bd, 0],
    [0xa9e4, 1],
    [0xa9e5, 0],
    [0xaa28, 1],
    [0xaa2e, 0],
    [0xaa30, 1],
    [0xaa32, 0],
    [0xaa34, 1],
    [0xaa36, 0],
    [0xaa42, 1],
    [0xaa43, 0],
    [0xaa4b, 1],
    [0xaa4c, 0],
    [0xaa7b, 1],
    [0xaa7c, 0],
    [0xaaaf, 1],
    [0xaab0, 0],
    [0xaab1, 1],
    [0xaab4, 0],
    [0xaab6, 1],
    [0xaab8, 0],
    [0xaabd, 1],
    [0xaabf, 0],
    [0xaac0, 1],
    [0xaac1, 0],
    [0xaaeb, 1],
    [0xaaed, 0],
    [0xaaf5, 1],
    [0xaaf6, 0],
    [0xabe4, 1],
    [0xabe5, 0],
    [0xabe7, 1],
    [0xabe8, 0],
    [0xabec, 1],
    [0xabed, 0],
    [0xabff, 1],
    [0xd7a3, 2],
    [0xd7af, 1],
    [0xd7c6, 0],
    [0xd7ca, 1],
    [0xd7fb, 0],
    [0xdfff, 1],
    [0xf8ff, -1],
    [0xfaff, 2],
    [0xfb1d, 1],
    [0xfb1e, 0],
    [0xfdff, 1],
    [0xfe0f, 0],
    [0xfe19, 2],
    [0xfe1f, 1],
    [0xfe2f, 0],
    [0xfe52, 2],
    [0xfe53, 1],
    [0xfe66, 2],
    [0xfe67, 1],
    [0xfe6b, 2],
    [0xff00, 1],
    [0xff60, 2],
    [0xffdf, 1],
    [0xffe6, 2],
    [0xfffc, 1],
    [0xfffd, -1],
    [0x101fc, 1],
    [0x101fd, 0],
    [0x102df, 1],
    [0x102e0, 0],
    [0x10375, 1],
    [0x1037a, 0],
    [0x10a00, 1],
    [0x10a03, 0],
    [0x10a04, 1],
    [0x10a06, 0],
    [0x10a0b, 1],
    [0x10a0f, 0],
    [0x10a37, 1],
    [0x10a3a, 0],
    [0x10a3e, 1],
    [0x10a3f, 0],
    [0x10ae4, 1],
    [0x10ae6, 0],
    [0x10d23, 1],
    [0x10d27, 0],
    [0x10d68, 1],
    [0x10d6d, 0],
    [0x10eaa, 1],
    [0x10eac, 0],
    [0x10efb, 1],
    [0x10eff, 0],
    [0x10f45, 1],
    [0x10f50, 0],
    [0x10f81, 1],
    [0x10f85, 0],
    [0x11000, 1],
    [0x11001, 0],
    [0x11037, 1],
    [0x11046, 0],
    [0x1106f, 1],
    [0x11070, 0],
    [0x11072, 1],
    [0x11074, 0],
    [0x1107e, 1],
    [0x11081, 0],
    [0x110b2, 1],
    [0x110b6, 0],
    [0x110b8, 1],
    [0x110ba, 0],
    [0x110c1, 1],
    [0x110c2, 0],
    [0x110ff, 1],
    [0x11102, 0],
    [0x11126, 1],
    [0x1112b, 0],
    [0x1112c, 1],
    [0x11134, 0],
    [0x11172, 1],
    [0x11173, 0],
    [0x1117f, 1],
    [0x11181, 0],
    [0x111b5, 1],
    [0x111be, 0],
    [0x111c8, 1],
    [0x111cc, 0],
    [0x111ce, 1],
    [0x111cf, 0],
    [0x1122e, 1],
    [0x11231, 0],
    [0x11233, 1],
    [0x11234, 0],
    [0x11235, 1],
    [0x11237, 0],
    [0x1123d, 1],
    [0x1123e, 0],
    [0x11240, 1],
    [0x11241, 0],
    [0x112de, 1],
    [0x112df, 0],
    [0x112e2, 1],
    [0x112ea, 0],
    [0x112ff, 1],
    [0x11301, 0],
    [0x1133a, 1],
    [0x1133c, 0],
    [0x1133f, 1],
    [0x11340, 0],
    [0x11365, 1],
    [0x1136c, 0],
    [0x1136f, 1],
    [0x11374, 0],
    [0x113ba, 1],
    [0x113c0, 0],
    [0x113cd, 1],
    [0x113ce, 0],
    [0x113cf, 1],
    [0x113d0, 0],
    [0x113d1, 1],
    [0x113d2, 0],
    [0x113e0, 1],
    [0x113e2, 0],
    [0x11437, 1],
    [0x1143f, 0],
    [0x11441, 1],
    [0x11444, 0],
    [0x11445, 1],
    [0x11446, 0],
    [0x1145d, 1],
    [0x1145e, 0],
    [0x114b2, 1],
    [0x114b8, 0],
    [0x114b9, 1],
    [0x114ba, 0],
    [0x114be, 1],
    [0x114c0, 0],
    [0x114c1, 1],
    [0x114c3, 0],
    [0x115b1, 1],
    [0x115b5, 0],
    [0x115bb, 1],
    [0x115bd, 0],
    [0x115be, 1],
    [0x115c0, 0],
    [0x115db, 1],
    [0x115dd, 0],
    [0x11632, 1],
    [0x1163a, 0],
    [0x1163c, 1],
    [0x1163d, 0],
    [0x1163e, 1],
    [0x11640, 0],
    [0x116aa, 1],
    [0x116ab, 0],
    [0x116ac, 1],
    [0x116ad, 0],
    [0x116af, 1],
    [0x116b5, 0],
    [0x116b6, 1],
    [0x116b7, 0],
    [0x1171c, 1],
    [0x1171d, 0],
    [0x1171e, 1],
    [0x1171f, 0],
    [0x11721, 1],
    [0x11725, 0],
    [0x11726, 1],
    [0x1172b, 0],
    [0x1182e, 1],
    [0x11837, 0],
    [0x11838, 1],
    [0x1183a, 0],
    [0x1193a, 1],
    [0x1193c, 0],
    [0x1193d, 1],
    [0x1193e, 0],
    [0x11942, 1],
    [0x11943, 0],
    [0x119d3, 1],
    [0x119d7, 0],
    [0x119d9, 1],
    [0x119db, 0],
    [0x119df, 1],
    [0x119e0, 0],
    [0x11a00, 1],
    [0x11a0a, 0],
    [0x11a32, 1],
    [0x11a38, 0],
    [0x11a3a, 1],
    [0x11a3e, 0],
    [0x11a46, 1],
    [0x11a47, 0],
    [0x11a50, 1],
    [0x11a56, 0],
    [0x11a58, 1],
    [0x11a5b, 0],
    [0x11a89, 1],
    [0x11a96, 0],
    [0x11a97, 1],
    [0x11a99, 0],
    [0x11c2f, 1],
    [0x11c36, 0],
    [0x11c37, 1],
    [0x11c3d, 0],
    [0x11c3e, 1],
    [0x11c3f, 0],
    [0x11c91, 1],
    [0x11ca7, 0],
    [0x11ca9, 1],
    [0x11cb0, 0],
    [0x11cb1, 1],
    [0x11cb3, 0],
    [0x11cb4, 1],
    [0x11cb6, 0],
    [0x11d30, 1],
    [0x11d36, 0],
    [0x11d39, 1],
    [0x11d3a, 0],
    [0x11d3b, 1],
    [0x11d3d, 0],
    [0x11d3e, 1],
    [0x11d45, 0],
    [0x11d46, 1],
    [0x11d47, 0],
    [0x11d8f, 1],
    [0x11d91, 0],
    [0x11d94, 1],
    [0x11d95, 0],
    [0x11d96, 1],
    [0x11d97, 0],
    [0x11ef2, 1],
    [0x11ef4, 0],
    [0x11eff, 1],
    [0x11f01, 0],
    [0x11f35, 1],
    [0x11f3a, 0],
    [0x11f3f, 1],
    [0x11f40, 0],
    [0x11f41, 1],
    [0x11f42, 0],
    [0x11f59, 1],
    [0x11f5a, 0],
    [0x1343f, 1],
    [0x13440, 0],
    [0x13446, 1],
    [0x13455, 0],
    [0x1611d, 1],
    [0x16129, 0],
    [0x1612c, 1],
    [0x1612f, 0],
    [0x16aef, 1],
    [0x16af4, 0],
    [0x16b2f, 1],
    [0x16b36, 0],
    [0x16f4e, 1],
    [0x16f4f, 0],
    [0x16f8e, 1],
    [0x16f92, 0],
    [0x16fdf, 1],
    [0x16fe3, 2],
    [0x16fe4, 0],
    [0x16fef, 1],
    [0x16ff1, 2],
    [0x16fff, 1],
    [0x187f7, 2],
    [0x187ff, 1],
    [0x18cd5, 2],
    [0x18cfe, 1],
    [0x18d08, 2],
    [0x1afef, 1],
    [0x1aff3, 2],
    [0x1aff4, 1],
    [0x1affb, 2],
    [0x1affc, 1],
    [0x1affe, 2],
    [0x1afff, 1],
    [0x1b122, 2],
    [0x1b131, 1],
    [0x1b132, 2],
    [0x1b14f, 1],
    [0x1b152, 2],
    [0x1b154, 1],
    [0x1b155, 2],
    [0x1b163, 1],
    [0x1b167, 2],
    [0x1b16f, 1],
    [0x1b2fb, 2],
    [0x1bc9c, 1],
    [0x1bc9e, 0],
    [0x1ceff, 1],
    [0x1cf2d, 0],
    [0x1cf2f, 1],
    [0x1cf46, 0],
    [0x1d166, 1],
    [0x1d169, 0],
    [0x1d17a, 1],
    [0x1d182, 0],
    [0x1d184, 1],
    [0x1d18b, 0],
    [0x1d1a9, 1],
    [0x1d1ad, 0],
    [0x1d241, 1],
    [0x1d244, 0],
    [0x1d2ff, 1],
    [0x1d356, 2],
    [0x1d35f, 1],
    [0x1d376, 2],
    [0x1d9ff, 1],
    [0x1da36, 0],
    [0x1da3a, 1],
    [0x1da6c, 0],
    [0x1da74, 1],
    [0x1da75, 0],
    [0x1da83, 1],
    [0x1da84, 0],
    [0x1da9a, 1],
    [0x1da9f, 0],
    [0x1daa0, 1],
    [0x1daaf, 0],
    [0x1dfff, 1],
    [0x1e006, 0],
    [0x1e007, 1],
    [0x1e018, 0],
    [0x1e01a, 1],
    [0x1e021, 0],
    [0x1e022, 1],
    [0x1e024, 0],
    [0x1e025, 1],
    [0x1e02a, 0],
    [0x1e08e, 1],
    [0x1e08f, 0],
    [0x1e12f, 1],
    [0x1e136, 0],
    [0x1e2ad, 1],
    [0x1e2ae, 0],
    [0x1e2eb, 1],
    [0x1e2ef, 0],
    [0x1e4eb, 1],
    [0x1e4ef, 0],
    [0x1e5ed, 1],
    [0x1e5ef, 0],
    [0x1e8cf, 1],
    [0x1e8d6, 0],
    [0x1e943, 1],
    [0x1e94a, 0],
    [0x1f003, 1],
    [0x1f004, 2],
    [0x1f0ce, 1],
    [0x1f0cf, 2],
    [0x1f0ff, 1],
    [0x1f10a, -1],
    [0x1f10f, 1],
    [0x1f12d, -1],
    [0x1f12f, 1],
    [0x1f169, -1],
    [0x1f16f, 1],
    [0x1f18d, -1],
    [0x1f18e, 2],
    [0x1f190, -1],
    [0x1f19a, 2],
    [0x1f1ac, -1],
    [0x1f1ff, 1],
    [0x1f202, 2],
    [0x1f20f, 1],
    [0x1f23b, 2],
    [0x1f23f, 1],
    [0x1f248, 2],
    [0x1f24f, 1],
    [0x1f251, 2],
    [0x1f25f, 1],
    [0x1f265, 2],
    [0x1f2ff, 1],
    [0x1f320, 2],
    [0x1f32c, 1],
    [0x1f335, 2],
    [0x1f336, 1],
    [0x1f37c, 2],
    [0x1f37d, 1],
    [0x1f393, 2],
    [0x1f39f, 1],
    [0x1f3ca, 2],
    [0x1f3ce, 1],
    [0x1f3d3, 2],
    [0x1f3df, 1],
    [0x1f3f0, 2],
    [0x1f3f3, 1],
    [0x1f3f4, 2],
    [0x1f3f7, 1],
    [0x1f43e, 2],
    [0x1f43f, 1],
    [0x1f440, 2],
    [0x1f441, 1],
    [0x1f4fc, 2],
    [0x1f4fe, 1],
    [0x1f53d, 2],
    [0x1f54a, 1],
    [0x1f54e, 2],
    [0x1f54f, 1],
    [0x1f567, 2],
    [0x1f579, 1],
    [0x1f57a, 2],
    [0x1f594, 1],
    [0x1f596, 2],
    [0x1f5a3, 1],
    [0x1f5a4, 2],
    [0x1f5fa, 1],
    [0x1f64f, 2],
    [0x1f67f, 1],
    [0x1f6c5, 2],
    [0x1f6cb, 1],
    [0x1f6cc, 2],
    [0x1f6cf, 1],
    [0x1f6d2, 2],
    [0x1f6d4, 1],
    [0x1f6d7, 2],
    [0x1f6db, 1],
    [0x1f6df, 2],
    [0x1f6ea, 1],
    [0x1f6ec, 2],
    [0x1f6f3, 1],
    [0x1f6fc, 2],
    [0x1f7df, 1],
    [0x1f7eb, 2],
    [0x1f7ef, 1],
    [0x1f7f0, 2],
    [0x1f90b, 1],
    [0x1f93a, 2],
    [0x1f93b, 1],
    [0x1f945, 2],
    [0x1f946, 1],
    [0x1f9ff, 2],
    [0x1fa6f, 1],
    [0x1fa7c, 2],
    [0x1fa7f, 1],
    [0x1fa89, 2],
    [0x1fa8e, 1],
    [0x1fac6, 2],
    [0x1facd, 1],
    [0x1fadc, 2],
    [0x1fade, 1],
    [0x1fae9, 2],
    [0x1faef, 1],
    [0x1faf8, 2],
    [0x1ffff, 1],
    [0x2fffd, 2],
    [0x2ffff, 1],
    [0x3fffd, 2],
    [0xe00ff, 1],
    [0xe01ef, 0],
    [0xeffff, 1],
    [0xffffd, -1],
    [0xfffff, 1],
    [0x10fffd, -1],
    [0x7fffffff, 1]
  ].transpose.map(&:freeze)
end

# -*- encoding: utf-8 -*-
# stub: bundler 2.7.0 ruby lib

Gem::Specification.new do |s|
  s.name = "bundler".freeze
  s.version = "2.7.0".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 3.4.1".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "bug_tracker_uri" => "https://github.com/rubygems/rubygems/issues?q=is%3Aopen+is%3Aissue+label%3ABundler", "changelog_uri" => "https://github.com/rubygems/rubygems/blob/master/bundler/CHANGELOG.md", "homepage_uri" => "https://bundler.io/", "source_code_uri" => "https://github.com/rubygems/rubygems/tree/master/bundler" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["Andr\u00E9 <PERSON>".freeze, "<PERSON>".freeze, "<PERSON>".freeze, "<PERSON><PERSON><PERSON>".freeze, "<PERSON>\u00EDguez".freeze, "<PERSON>".freeze, "<PERSON>o".freeze, "<PERSON>".freeze, "James <PERSON>".freeze, "Tim Moore".freeze, "Andr\u00E9 Medeiros".freeze, "<PERSON> <PERSON> Suttles".freeze, "<PERSON> Lee".freeze, "Carl Lerche".freeze, "Yehuda Katz".freeze]
  s.bindir = "exe".freeze
  s.date = "1980-01-02"
  s.description = "Bundler manages an application's dependencies through its entire life, across many machines, systematically and repeatably".freeze
  s.email = ["<EMAIL>".freeze]
  s.executables = ["bundle".freeze, "bundler".freeze]
  s.files = ["exe/bundle".freeze, "exe/bundler".freeze]
  s.homepage = "https://bundler.io".freeze
  s.licenses = ["MIT".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 3.2.0".freeze)
  s.rubygems_version = "3.7.0".freeze
  s.summary = "The best way to manage your application's dependencies".freeze

  s.installed_by_version = "3.6.9".freeze
end

# -*- encoding: utf-8 -*-
# stub: base64 0.3.0 ruby lib

Gem::Specification.new do |s|
  s.name = "base64".freeze
  s.version = "0.3.0".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "changelog_uri" => "https://github.com/ruby/base64/releases", "homepage_uri" => "https://github.com/ruby/base64", "source_code_uri" => "https://github.com/ruby/base64" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON><PERSON>".freeze]
  s.bindir = "exe".freeze
  s.date = "1980-01-02"
  s.description = "Support for encoding and decoding binary data using a Base64 representation.".freeze
  s.email = ["<EMAIL>".freeze]
  s.homepage = "https://github.com/ruby/base64".freeze
  s.licenses = ["Ruby".freeze, "BSD-2-Clause".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.4".freeze)
  s.rubygems_version = "3.6.7".freeze
  s.summary = "Support for encoding and decoding binary data using a Base64 representation.".freeze

  s.installed_by_version = "3.6.9".freeze
end

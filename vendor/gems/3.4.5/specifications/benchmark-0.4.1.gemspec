# -*- encoding: utf-8 -*-
# stub: benchmark 0.4.1 ruby lib

Gem::Specification.new do |s|
  s.name = "benchmark".freeze
  s.version = "0.4.1".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "homepage_uri" => "https://github.com/ruby/benchmark", "source_code_uri" => "https://github.com/ruby/benchmark" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON><PERSON><PERSON>".freeze]
  s.bindir = "exe".freeze
  s.date = "2025-05-30"
  s.description = "a performance benchmarking library".freeze
  s.email = ["<EMAIL>".freeze]
  s.homepage = "https://github.com/ruby/benchmark".freeze
  s.licenses = ["Ruby".freeze, "BSD-2-Clause".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.1.0".freeze)
  s.rubygems_version = "3.5.11".freeze
  s.summary = "a performance benchmarking library".freeze

  s.installed_by_version = "3.6.9".freeze
end

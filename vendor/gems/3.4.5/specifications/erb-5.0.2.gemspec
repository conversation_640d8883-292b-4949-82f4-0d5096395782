# -*- encoding: utf-8 -*-
# stub: erb 5.0.2 ruby lib
# stub: ext/erb/escape/extconf.rb

Gem::Specification.new do |s|
  s.name = "erb".freeze
  s.version = "5.0.2".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "homepage_uri" => "https://github.com/ruby/erb", "source_code_uri" => "https://github.com/ruby/erb" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["Masatoshi SEKI".freeze, "<PERSON><PERSON><PERSON>".freeze]
  s.bindir = "libexec".freeze
  s.date = "1980-01-02"
  s.description = "An easy to use but powerful templating system for Ruby.".freeze
  s.email = ["<EMAIL>".freeze, "<EMAIL>".freeze]
  s.executables = ["erb".freeze]
  s.extensions = ["ext/erb/escape/extconf.rb".freeze]
  s.files = ["ext/erb/escape/extconf.rb".freeze, "libexec/erb".freeze]
  s.homepage = "https://github.com/ruby/erb".freeze
  s.licenses = ["Ruby".freeze, "BSD-2-Clause".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 3.2.0".freeze)
  s.rubygems_version = "3.6.7".freeze
  s.summary = "An easy to use but powerful templating system for Ruby.".freeze

  s.installed_by_version = "3.6.9".freeze
end

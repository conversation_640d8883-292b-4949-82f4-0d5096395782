# -*- encoding: utf-8 -*-
# stub: io-console 0.8.1 ruby lib
# stub: ext/io/console/extconf.rb

Gem::Specification.new do |s|
  s.name = "io-console".freeze
  s.version = "0.8.1".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "changelog_uri" => "https://github.com/ruby/io-console/releases", "source_code_url" => "https://github.com/ruby/io-console" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["Nobu Nakada".freeze]
  s.date = "2025-07-15"
  s.description = "add console capabilities to IO instances.".freeze
  s.email = "<EMAIL>".freeze
  s.extensions = ["ext/io/console/extconf.rb".freeze]
  s.files = ["ext/io/console/extconf.rb".freeze]
  s.homepage = "https://github.com/ruby/io-console".freeze
  s.licenses = ["Ruby".freeze, "BSD-2-Clause".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.6.0".freeze)
  s.rubygems_version = "3.6.7".freeze
  s.summary = "Console interface".freeze

  s.installed_by_version = "3.6.9".freeze
end

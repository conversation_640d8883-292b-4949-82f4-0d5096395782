# -*- encoding: utf-8 -*-
# stub: rubocop-rails-omakase 1.1.0 ruby lib

Gem::Specification.new do |s|
  s.name = "rubocop-rails-omakase".freeze
  s.version = "1.1.0".freeze

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON>".freeze]
  s.date = "2025-02-25"
  s.email = "<EMAIL>".freeze
  s.homepage = "https://github.com/rails/rubocop-rails-omakase".freeze
  s.licenses = ["MIT".freeze]
  s.rubygems_version = "3.5.22".freeze
  s.summary = "Omakase Ruby styling for Rails".freeze

  s.installed_by_version = "3.6.9".freeze

  s.specification_version = 4

  s.add_runtime_dependency(%q<rubocop>.freeze, [">= 1.72".freeze])
  s.add_runtime_dependency(%q<rubocop-rails>.freeze, [">= 2.30".freeze])
  s.add_runtime_dependency(%q<rubocop-performance>.freeze, [">= 1.24".freeze])
end

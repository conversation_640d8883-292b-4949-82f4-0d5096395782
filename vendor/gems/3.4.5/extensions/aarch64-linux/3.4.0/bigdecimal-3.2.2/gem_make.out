current directory: /workspace/sentry/vendor/gems/3.4.5/gems/bigdecimal-3.2.2/ext/bigdecimal
/usr/local/bin/ruby extconf.rb
checking for __builtin_clz()... yes
checking for __builtin_clzl()... yes
checking for __builtin_clzll()... yes
checking for float.h... yes
checking for math.h... yes
checking for stdbool.h... yes
checking for stdlib.h... yes
checking for x86intrin.h... no
checking for intrin.h... no
checking for labs() in stdlib.h... yes
checking for llabs() in stdlib.h... yes
checking for finite() in math.h... yes
checking for isfinite() in math.h... no
checking for ruby/atomic.h... yes
checking for ruby/internal/has/builtin.h... yes
checking for ruby/internal/static_assert.h... yes
checking for rb_rational_num() in ruby.h... yes
checking for rb_rational_den() in ruby.h... yes
checking for rb_complex_real() in ruby.h... yes
checking for rb_complex_imag() in ruby.h... yes
checking for rb_opts_exception_p() in ruby.h... yes
checking for rb_category_warn() in ruby.h... yes
checking for RB_WARN_CATEGORY_DEPRECATED in ruby.h... yes
creating Makefile

current directory: /workspace/sentry/vendor/gems/3.4.5/gems/bigdecimal-3.2.2/ext/bigdecimal
make DESTDIR\= sitearchdir\=./.gem.20250804-968-2acjvz sitelibdir\=./.gem.20250804-968-2acjvz clean
make: Warning: File 'Makefile' has modification time 0.0013 s in the future
make: warning:  Clock skew detected.  Your build may be incomplete.

current directory: /workspace/sentry/vendor/gems/3.4.5/gems/bigdecimal-3.2.2/ext/bigdecimal
make DESTDIR\= sitearchdir\=./.gem.20250804-968-2acjvz sitelibdir\=./.gem.20250804-968-2acjvz
compiling bigdecimal.c
compiling missing.c
linking shared-object bigdecimal.so

current directory: /workspace/sentry/vendor/gems/3.4.5/gems/bigdecimal-3.2.2/ext/bigdecimal
make DESTDIR\= sitearchdir\=./.gem.20250804-968-2acjvz sitelibdir\=./.gem.20250804-968-2acjvz install
make: Warning: File 'bigdecimal.so' has modification time 0.001 s in the future
/usr/bin/install -c -m 0755 bigdecimal.so ./.gem.20250804-968-2acjvz
make: warning:  Clock skew detected.  Your build may be incomplete.

current directory: /workspace/sentry/vendor/gems/3.4.5/gems/bigdecimal-3.2.2/ext/bigdecimal
make DESTDIR\= sitearchdir\=./.gem.20250804-968-2acjvz sitelibdir\=./.gem.20250804-968-2acjvz clean

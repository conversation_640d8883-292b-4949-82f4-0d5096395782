current directory: /workspace/sentry/vendor/gems/3.4.5/gems/io-console-0.8.1/ext/io/console
/usr/local/bin/ruby extconf.rb
checking for rb_syserr_fail_str(0, Qnil)... yes
checking for rb_interned_str_cstr()... yes
checking for rb_io_path() in ruby/io.h... yes
checking for rb_io_descriptor() in ruby/io.h... yes
checking for rb_io_get_write_io() in ruby/io.h... yes
checking for rb_io_closed_p() in ruby/io.h... yes
checking for rb_io_open_descriptor() in ruby/io.h... yes
checking for rb_ractor_local_storage_value_newkey()... yes
checking for termios.h... yes
checking for cfmakeraw() in termios.h... yes
checking for sys/ioctl.h... yes
checking for HAVE_RUBY_FIBER_SCHEDULER_H... yes
checking for ttyname_r()... yes
creating Makefile

current directory: /workspace/sentry/vendor/gems/3.4.5/gems/io-console-0.8.1/ext/io/console
make DESTDIR\= sitearchdir\=./.gem.20250804-968-f9j65u sitelibdir\=./.gem.20250804-968-f9j65u clean
make: Warning: File 'Makefile' has modification time 0.0015 s in the future
make: warning:  Clock skew detected.  Your build may be incomplete.

current directory: /workspace/sentry/vendor/gems/3.4.5/gems/io-console-0.8.1/ext/io/console
make DESTDIR\= sitearchdir\=./.gem.20250804-968-f9j65u sitelibdir\=./.gem.20250804-968-f9j65u
compiling console.c
linking shared-object io/console.so

current directory: /workspace/sentry/vendor/gems/3.4.5/gems/io-console-0.8.1/ext/io/console
make DESTDIR\= sitearchdir\=./.gem.20250804-968-f9j65u sitelibdir\=./.gem.20250804-968-f9j65u install
make: Warning: File 'console.so' has modification time 0.0014 s in the future
/usr/bin/install -c -m 0755 console.so ./.gem.20250804-968-f9j65u/io
make: warning:  Clock skew detected.  Your build may be incomplete.

current directory: /workspace/sentry/vendor/gems/3.4.5/gems/io-console-0.8.1/ext/io/console
make DESTDIR\= sitearchdir\=./.gem.20250804-968-f9j65u sitelibdir\=./.gem.20250804-968-f9j65u clean

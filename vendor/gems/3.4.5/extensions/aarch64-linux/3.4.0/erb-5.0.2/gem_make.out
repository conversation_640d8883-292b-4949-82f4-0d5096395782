current directory: /workspace/sentry/vendor/gems/3.4.5/gems/erb-5.0.2/ext/erb/escape
/usr/local/bin/ruby extconf.rb
checking for rb_ext_ractor_safe() in ruby.h... yes
creating Makefile

current directory: /workspace/sentry/vendor/gems/3.4.5/gems/erb-5.0.2/ext/erb/escape
make DESTDIR\= sitearchdir\=./.gem.20250804-968-lzdfvv sitelibdir\=./.gem.20250804-968-lzdfvv clean
make: Warning: File 'Makefile' has modification time 0.00081 s in the future
make: warning:  Clock skew detected.  Your build may be incomplete.

current directory: /workspace/sentry/vendor/gems/3.4.5/gems/erb-5.0.2/ext/erb/escape
make DESTDIR\= sitearchdir\=./.gem.20250804-968-lzdfvv sitelibdir\=./.gem.20250804-968-lzdfvv
compiling escape.c
linking shared-object erb/escape.so

current directory: /workspace/sentry/vendor/gems/3.4.5/gems/erb-5.0.2/ext/erb/escape
make DESTDIR\= sitearchdir\=./.gem.20250804-968-lzdfvv sitelibdir\=./.gem.20250804-968-lzdfvv install
make: Warning: File 'escape.so' has modification time 0.0016 s in the future
/usr/bin/install -c -m 0755 escape.so ./.gem.20250804-968-lzdfvv/erb
make: warning:  Clock skew detected.  Your build may be incomplete.

current directory: /workspace/sentry/vendor/gems/3.4.5/gems/erb-5.0.2/ext/erb/escape
make DESTDIR\= sitearchdir\=./.gem.20250804-968-lzdfvv sitelibdir\=./.gem.20250804-968-lzdfvv clean
